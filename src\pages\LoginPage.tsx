import { Link, useNavigate } from "react-router";
import login_image from "../assets/images/login_image.png";
import { useState, FormEvent, useEffect } from "react";
import login_bg_image from "../assets/images/login_bg.png";
import login_wave_image from "../assets/images/login_wave.png";
import { useTranslation } from "react-i18next";
import toast from "react-hot-toast";
import endpoint from "../api/endpoint";
import axios from "axios";

interface FormErrors {
  username?: string;
  password?: string;
}
declare global {
  interface Window {
    ssoUrl: string;
    checkInternal: string;
  }
}

export default function LoginPage() {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [rememberMe, setRememberMe] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showSso, setShowSso] = useState(false);

  const [formData, setFormData] = useState({
    username: "",
    password: "",
  });
  const [errors, setErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const queryParams = new URLSearchParams(location.search);
  const redirectUrl = queryParams.get("redirect") || null;

  if (redirectUrl) {
    localStorage.setItem("redirect", redirectUrl);

    // Remove the redirect param from the URL without reloading
    queryParams.delete("redirect");
    const newUrl =
      window.location.pathname +
      (queryParams.toString() ? "?" + queryParams.toString() : "");
    window.history.replaceState({}, "", newUrl);
  }

  const isAuthenticated = localStorage.getItem("user"); // example check

  useEffect(() => {
    if (isAuthenticated) {
      navigate("/"); // redirect to home
    } else {
      axios.get(window.checkInternal).then(() => {
        setShowSso(true);
      });
    }
  }, []);

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};
    let isValid = true;

    // Username validation
    if (!formData.username.trim()) {
      newErrors.username = "يرجى إدخال اسم المستخدم أو البريد الإلكتروني";
      isValid = false;
    }

    // Password validation
    if (!formData.password) {
      newErrors.password = "يرجى إدخال كلمة المرور";
      isValid = false;
    } else if (formData.password.length < 8) {
      newErrors.password = "يجب أن تكون كلمة المرور 8 أحرف على الأقل";
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
    // Clear error when user starts typing
    if (errors[name as keyof FormErrors]) {
      setErrors((prev) => ({
        ...prev,
        [name]: undefined,
      }));
    }
  };

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    if (validateForm()) {
      try {
        const { data } = await endpoint.post(`/auth`, {
          username: formData.username,
          password: formData.password,
        });

        localStorage.setItem("token", data.token);
        localStorage.setItem("esriToken", data.esriToken);
        localStorage.setItem("user", JSON.stringify(data));

        toast.success("تم تسجيل الدخول بنجاح");

        if (localStorage.getItem("redirect")) {
          let redirect = localStorage.getItem("redirect");
          localStorage.removeItem("redirect");
          window.open(redirect || "/", "_self");
        } else navigate("/my-apps");
      } catch (error) {
        toast.error("اسم المستخدم أو كلمة المرور غير صحيحة");
      }
    }

    setIsSubmitting(false);
  };

  return (
    <div className="login-page">
      <div
        className="login-bg"
        style={{ backgroundImage: `url(${login_bg_image})` }}
      ></div>
      <div
        className="login-wave"
        style={{ backgroundImage: `url(${login_wave_image})` }}
      ></div>
      {/* <div className="overlay"></div> */}
      <div className="container">
        <div style={{ padding: "40px 0", width: "800px", maxWidth: "100%" }}>
          <div className="login-container">
            <form className="login-form" onSubmit={handleSubmit}>
              <h2 className="login-title">{t("loginPage.title")}</h2>

              <p className="login-subtitle">{t("loginPage.demoMode")}</p>
              {/* <p className="login-subtitle">{t("loginPage.subtitle")}</p> */}

              <button type="button" className="login-button">
                {t("loginPage.nationalAccess")}
              </button>
              {showSso && (
                <button
                  type="button"
                  className="login-button"
                  onClick={() => window.open(`${window.ssoUrl}`, "_self")}
                >
                  {t("loginPage.staffLogin")}
                </button>
              )}

              <div className="divider-container">
                <div className="divider-line"></div>
                <div className="divider-text">{t("loginPage.or")}</div>
                <div className="divider-line"></div>
              </div>

              <div className="input-group">
                <input
                  type="text"
                  name="username"
                  value={formData.username}
                  onChange={handleInputChange}
                  placeholder={t("loginPage.usernamePlaceholder")}
                  className={`login-input ${errors.username ? "error" : ""}`}
                />
              </div>
              {errors.username && (
                <span className="error-message">{errors.username}</span>
              )}

              <div className="input-group">
                <input
                  type={showPassword ? "text" : "password"}
                  name="password"
                  value={formData.password}
                  onChange={handleInputChange}
                  placeholder={t("loginPage.passwordPlaceholder")}
                  className={`login-input ${errors.password ? "error" : ""}`}
                />
                <button
                  type="button"
                  className="password-toggle"
                  onClick={() => setShowPassword(!showPassword)}
                  tabIndex={-1}
                >
                  {showPassword ? (
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="20"
                      height="20"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1-5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24" />
                      <line x1="1" y1="1" x2="23" y2="23" />
                    </svg>
                  ) : (
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="20"
                      height="20"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z" />
                      <circle cx="12" cy="12" r="3" />
                    </svg>
                  )}
                </button>
              </div>
              {errors.password && (
                <span className="error-message">{errors.password}</span>
              )}

              <div className="remember-me-container">
                <label className="switch">
                  <input
                    type="checkbox"
                    checked={rememberMe}
                    onChange={(e) => setRememberMe(e.target.checked)}
                  />
                  <span className="slider"></span>
                </label>
                <span className="remember-me-text">
                  {t("loginPage.rememberMe")}
                </span>
              </div>

              <button
                type="submit"
                className="login-button login-button-primary"
                disabled={isSubmitting}
              >
                {isSubmitting ? "جاري تسجيل الدخول..." : t("loginPage.login")}
              </button>

              <Link to={"#"} className="forgot-password-link">
                {t("loginPage.forgotPassword")}
              </Link>

              <div className="form-divider"></div>

              <Link to={"#"} className="create-account-link">
                {t("loginPage.createAccount")}
              </Link>
            </form>
            <div className="login-image-container">
              <img
                src={login_image}
                alt="login image"
                className="login-image"
              />
            </div>
          </div>

          <div className="footer-text">{t("footer.copyright")}</div>
        </div>
      </div>
    </div>
  );
}
