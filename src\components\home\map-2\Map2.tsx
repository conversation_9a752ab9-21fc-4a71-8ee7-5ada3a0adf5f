import {
  pointImages,
  pointBgImages,
  stats as statsData,
  points as pointsData,
} from "./map2Data";

import ncw_map_bg1 from "../../../assets/images/ncw-map/bg1.webp";
import ncw_map_bg2 from "../../../assets/images/ncw-map/bg2.webp";

import img_2024 from "../../../assets/images/ncw-map/2017.webp";
import img_2030 from "../../../assets/images/ncw-map/2030.webp";
import img_2025 from "../../../assets/images/ncw-map/2025.webp";

import { useState, useMemo } from "react";
import { useTranslation } from "react-i18next";

import no_border_image from "../../../assets/images/ncw-map/بلا حدود.webp";
import point_1 from "../../../assets/images/ncw-map/أشجار و مناطق الغابات الجبلية.webp";
import point_2 from "../../../assets/images/ncw-map/البحر الاحمر.webp";
import point_3 from "../../../assets/images/ncw-map/الخليج العربي.webp";
import point_4 from "../../../assets/images/ncw-map/الرمال العربية.webp";
import point_5 from "../../../assets/images/ncw-map/السهل الساحلي الغربي.webp";
import point_6 from "../../../assets/images/ncw-map/السهل الساحلي للخليج العربي.webp";
import point_7 from "../../../assets/images/ncw-map/المرتفعات الغربية.webp";
import point_8 from "../../../assets/images/ncw-map/الهضاب العربية.webp";
import point_9 from "../../../assets/images/ncw-map/صحراء المنطقة الشمالية.webp";

import MapImage from "./MapImage";
import PreloadImages from "./PreloadImages";
import ProtectedAreasOverlay from "./ProtectedAreasOverlay";
import EnvironmentalRegionsOverlay from "./EnvironmentalRegionsOverlay";
import { Link } from "react-router";
import { urls } from "../../../utils/urls";

export default function Map2() {
  const { t } = useTranslation();
  const [selectedIimage, setSelectedIimage] = useState(img_2024);
  const [selectedTab, setSelectedTab] = useState(1);
  const [selectedInnerTab, setSelectedInnerTab] = useState("2017");
  const [activePoint, setActivePoint] = useState<number | null>(null);
  const [isTransitioning] = useState(false);
  const [hoveredPoint, setHoveredPoint] = useState<number | null>(null);
  const [sectionBgImage, setSectionBgImage] = useState(ncw_map_bg2);

  // Memoize translated points and stats
  const translatedPoints = useMemo(
    () =>
      pointsData.map((p, i) => ({
        ...p,
        name: t(`map2.points.${i}.name`),
        description: t(`map2.points.${i}.description`),
        type: t(`map2.points.${i}.type`),
        cardTitle: t(
          `map2.environmentalRegions.regions.${p.cardTitle
            .replace("t(map2.environmentalRegions.regions.", "")
            .replace(".title)", "")}.title`
        ),
        // cardSubtitle: t(
        //   `map2.environmentalRegions.regions.${p.cardSubtitle
        //     .replace("t(map2.environmentalRegions.regions.", "")
        //     .replace(".subtitle)", "")}.subtitle`
        // ),
        cardDescription: t(
          `map2.environmentalRegions.regions.${p.cardDescription
            .replace("t(map2.environmentalRegions.regions.", "")
            .replace(".description)", "")}.description`
        ),
      })),
    [t]
  );

  const translatedStats = useMemo(
    () => ({
      "2017": { ...statsData["2017"] },
      "2025": { ...statsData["2025"] },
      "2030": { ...statsData["2030"] },
    }),
    [statsData]
  );

  // Handle inner tab change and image swap for المناطق المحمية
  const handleInnerTab = (year: string) => {
    setSelectedInnerTab(year);
    if (selectedTab === 1) {
      // Only change image for المناطق المحمية tab
      if (year === "2030") setSelectedIimage(img_2030);
      else if (year === "2025") setSelectedIimage(img_2025);
      else setSelectedIimage(img_2024); // Now
    }
  };

  // Handle point click for الأقاليم البيئية
  const handlePointClick = (pointIndex: number) => {
    const newActivePoint = activePoint === pointIndex ? null : pointIndex;
    setActivePoint(newActivePoint);

    // Change image based on clicked point for الأقاليم البيئية
    if (selectedTab === 2) {
      if (newActivePoint !== null) {
        setSectionBgImage(pointBgImages[pointIndex]);
        switch (pointIndex) {
          case 0:
            setSelectedIimage(point_1);
            break;
          case 1:
            setSelectedIimage(point_2);
            break;
          case 2:
            setSelectedIimage(point_3);
            break;
          case 3:
            setSelectedIimage(point_4);
            break;
          case 4:
            setSelectedIimage(point_5);
            break;
          case 5:
            setSelectedIimage(point_6);
            break;
          case 6:
            setSelectedIimage(point_7);
            break;
          case 7:
            setSelectedIimage(point_8);
            break;
          case 8:
            setSelectedIimage(point_9);
            break;
          default:
            setSelectedIimage(no_border_image);
        }
      } else {
        setSectionBgImage(ncw_map_bg2);
        setSelectedIimage(no_border_image);
      }
    }
  };

  // Handle main tab change
  const handleMainTabChange = (tabNumber: number) => {
    setSelectedTab(tabNumber);
    setActivePoint(null); // Reset active point when changing tabs
    if (tabNumber === 1) {
      // المناطق المحمية - use year-based images
      if (selectedInnerTab === "2030") setSelectedIimage(img_2030);
      else if (selectedInnerTab === "2025") setSelectedIimage(img_2025);
      else setSelectedIimage(img_2024);
    } else if (tabNumber === 2) {
      // الأقاليم البيئية - use ecosystem images
      setSelectedIimage(no_border_image); // Default image for environmental regions
      setSectionBgImage(ncw_map_bg2); // Reset background image to default
    }
  };

  return (
    <section
      className="ncw-map"
      id="ncwMap"
      style={{
        position: "relative",
        // minHeight: "calc(100vh - 73px)",
        minHeight: "100vh",
        // paddingTop: "60px",
        background:
          selectedTab === 2
            ? `url(${sectionBgImage}) center/cover no-repeat`
            : "linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 50%, #1a1a1a 100%)",
        overflow: "hidden",
      }}
    >
      <PreloadImages
        pointImages={pointImages}
        noBorderImage={pointImages[0]}
        bgImages={pointBgImages}
        defaultBg={pointBgImages[1]}
      />
      {/* Background Images */}
      <img
        src={ncw_map_bg1}
        alt="bg1"
        // loading="lazy"
        className="map-bg"
        style={{
          position: "absolute",
          top: 0,
          left: 0,
          width: "100%",
          height: "100%",
          objectFit: "cover",
          opacity: selectedTab === 1 ? 1 : 0,
          transition: "opacity 0.8s cubic-bezier(0.4, 0, 0.2, 1)",
          zIndex: 1,
          animation:
            selectedTab === 1 ? "zoomInOut 80s ease-in-out infinite" : "none",
        }}
      />
      <img
        src={sectionBgImage}
        alt="bg2"
        // loading="lazy"
        className="map-bg"
        style={{
          position: "absolute",
          top: 0,
          left: 0,
          width: "100%",
          height: "100%",
          objectFit: "cover",
          opacity: selectedTab === 2 ? 1 : 0,
          transition: "opacity 0.8s cubic-bezier(0.4, 0, 0.2, 1)",
          zIndex: 1,
          animation:
            selectedTab === 2 ? "panAndZoom 200s ease-in-out infinite" : "none",
        }}
      />
      {/* Modern gradient overlay */}
      <div
        style={{
          position: "absolute",
          top: 0,
          left: 0,
          width: "100%",
          height: "100%",
          background: "rgba(0,0,0,0.30)",
          zIndex: 2,
          pointerEvents: "none",
        }}
      />
      {/* Title and description with modern styling */}
      {/* <div
        style={{
          // padding: "30px 0 20px 0",
          // marginTop: "100px",
          marginTop: "6%",
          textAlign: "center",
          color: "#fff",
          // position: "relative",
          zIndex: 3,
        }}
        className="container-fluid"
      >
        <MapHeader t={t} />
      </div> */}
      {/* Main flex layout: stats | inner tabs | map | main tabs */}
      <div
        style={{
          display: "flex",
          flexDirection: "row",
          justifyContent: "center",
          alignItems: "flex-start",
          // width: "100%",
          // maxWidth: 1400,
          // margin: "0 auto",
          // position: "relative",
          zIndex: 3,
          position: "absolute",
          top: "50%",
          left: "50%",
          transform: "translate(-50%, -50%)",
        }}
        className="container-fluid"
      >
        {/* Map image (center, overlay above background) */}
        <div
          style={{
            flex: 1,
            display: "flex",
            // justifyContent: "flex-end",
            justifyContent: "center",
            alignItems: "center",
            minHeight: 500,
            position: "relative",
            // paddingRight: "3%",
            paddingRight: "12%",
            // marginLeft: "-120px",
            marginLeft: "-50px",
            // height: "100vh",
            marginTop: "10%",
          }}
        >
          {/* Map container that holds both image and points */}
          <div
            style={{
              position: "relative",
              display: "inline-block",
            }}
          >
            <MapImage
              src={selectedIimage}
              alt="map"
              isTransitioning={isTransitioning}
            />

            {selectedTab === 2 && (
              <EnvironmentalRegionsOverlay
                points={translatedPoints}
                activePoint={activePoint}
                hoveredPoint={hoveredPoint}
                setHoveredPoint={setHoveredPoint}
                handlePointClick={handlePointClick}
              />
            )}
          </div>

          {/* Protected Areas Overlay - positioned relative to viewport */}
          {selectedTab === 1 && (
            <ProtectedAreasOverlay
              stats={translatedStats}
              selectedInnerTab={selectedInnerTab}
              handleInnerTab={handleInnerTab}
              t={t}
            />
          )}

          {/* Environmental Regions Description - positioned relative to viewport */}
          {selectedTab === 2 && (
            <div
              style={{
                position: "absolute",
                // top: "32px",
                top: "10%",
                insetInlineStart: "0%",
                color: "#fff",
                zIndex: 10,
                width: "28%",
                textAlign: "start",
                background: "none",
                border: "none",
                marginTop: "15px",
                boxShadow: "none",
                padding: 0,
                maxHeight: 340,
                display: "flex",
                flexDirection: "column",
              }}
            >
              <style>{`
                .env-desc-scroll::-webkit-scrollbar {
                  width: 7px;
                  background: transparent;
                }
                .env-desc-scroll::-webkit-scrollbar-thumb {
                  background: rgba(127,205,255,0.18);
                  border-radius: 8px;
                }
                .env-desc-scroll::-webkit-scrollbar-thumb:hover {
                  background: rgba(127,205,255,0.32);
                }
                .env-desc-scroll {
                  scrollbar-width: thin;
                  scrollbar-color: rgba(127,205,255,0.18) transparent;
                }
              `}</style>
              <div style={{ flex: "0 0 auto", background: "none" }}>
                <h2
                  style={{
                    color: "#fff",
                    fontSize: "22px",
                    fontWeight: 700,
                    margin: 0,
                    marginBottom: "10px",
                    wordBreak: "break-word",
                    hyphens: "auto",
                  }}
                >
                  {activePoint !== null
                    ? translatedPoints[activePoint].cardTitle
                    : t("map2.environmentalRegions.title")}
                </h2>
              </div>
              <div
                className="env-desc-scroll"
                style={{
                  flex: "1 1 0%",
                  overflowY: "auto",
                  minHeight: 0,
                  maxHeight: 260,
                }}
              >
                <p
                  style={{
                    color: "#fff",
                    fontSize: "15px",
                    fontWeight: 400,
                    margin: 0,
                    wordBreak: "break-word",
                    textAlign: "justify",
                    hyphens: "auto",
                  }}
                >
                  {activePoint !== null
                    ? translatedPoints[activePoint].cardDescription
                    : t("map2.environmentalRegions.description")}
                </p>
              </div>
            </div>
          )}
        </div>
        {/* Main tabs (vertical, right) */}
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            justifyContent: "center",
            alignItems: "flex-start",
            minWidth: "180px",
            // padding: "0 20px",
            padding: "0",
            gap: "16px",
            // marginTop: "30px",
            // marginTop: "96px",
            marginTop: "20%",
            zIndex: 1000,
          }}
        >
          <button
            className={`nav-link ${selectedTab === 1 ? "active" : ""}`}
            style={{
              background:
                selectedTab === 1
                  ? "rgba(180, 84, 52, 0.7)"
                  : "rgba(0,0,0,0.08)",
              // background:
              //   selectedTab === 1
              //     ? "linear-gradient(90deg, #b45434 0%, #7fcdff 100%)"
              //     : "rgba(0,0,0,0.15)",
              border:
                selectedTab === 1
                  ? "2px solid rgba(180, 84, 52, 0.7)"
                  : "2px solid rgba(68, 68, 68, 0.5)",
              borderRadius: "16px",
              color: selectedTab === 1 ? "#fff" : "#fff",
              fontWeight: selectedTab === 1 ? 800 : 500,
              fontSize: "20px",
              padding: "18px 32px",
              cursor: "pointer",
              textAlign: "center",
              width: "230px",
              textDecoration: "none",
              boxShadow:
                selectedTab === 1
                  ? "0 4px 16px rgba(180, 84, 52, 0.12)"
                  : "0 2px 8px rgba(0,0,0,0.04)",
              transition: "all 0.22s cubic-bezier(0.4,0,0.2,1)",
              outline: "none",
              marginBottom: "18px",
              letterSpacing: "0.01em",
              position: "relative",
            }}
            onClick={() => handleMainTabChange(1)}
            onMouseOver={(e) => {
              if (selectedTab !== 1)
                e.currentTarget.style.background = "rgba(180,84,52,0.08)";
            }}
            onMouseOut={(e) => {
              if (selectedTab !== 1)
                e.currentTarget.style.background = "rgba(0,0,0,0.08)";
            }}
            aria-current={selectedTab === 1 ? "page" : undefined}
          >
            {t("map2.tabs.protectedAreas")}
          </button>
          <button
            className={`nav-link ${selectedTab === 2 ? "active" : ""}`}
            style={{
              background:
                selectedTab === 2
                  ? "rgba(180, 84, 52, 0.7)"
                  : "rgba(0,0,0,0.08)",
              // background:
              //   selectedTab === 2
              //     ? "linear-gradient(90deg, #b45434 0%, #7fcdff 100%)"
              //     : "rgba(0,0,0,0.15)",
              border:
                selectedTab === 2
                  ? "2px solid rgba(180, 84, 52, 0.7)"
                  : "2px solid rgba(68, 68, 68, 0.5)",
              borderRadius: "16px",
              color: selectedTab === 2 ? "#fff" : "#fff",
              fontWeight: selectedTab === 2 ? 800 : 500,
              fontSize: "20px",
              padding: "18px 32px",
              cursor: "pointer",
              textAlign: "center",
              width: "230px",
              textDecoration: "none",
              boxShadow:
                selectedTab === 2
                  ? "0 4px 16px rgba(180, 84, 52, 0.12)"
                  : "0 2px 8px rgba(0,0,0,0.04)",
              transition: "all 0.22s cubic-bezier(0.4,0,0.2,1)",
              outline: "none",
              marginBottom: "18px",
              letterSpacing: "0.01em",
              position: "relative",
            }}
            onClick={() => handleMainTabChange(2)}
            onMouseOver={(e) => {
              if (selectedTab !== 2)
                e.currentTarget.style.background = "rgba(180,84,52,0.08)";
            }}
            onMouseOut={(e) => {
              if (selectedTab !== 2)
                e.currentTarget.style.background = "rgba(0,0,0,0.08)";
            }}
            aria-current={selectedTab === 2 ? "page" : undefined}
          >
            {t("map2.tabs.environmentalRegions")}
          </button>
          {/* Wildlife Explorer button below the main tabs */}
          <Link
            to={urls.wildlifeExplorer}
            target="_blank"
            style={{
              // width: "230px",
              // marginTop: "70px",
              position: "absolute",
              bottom: "100px",
              left: "32px",
            }}
          >
            <button
              // className="btn btn-outline-light w-100 explorer-link-btn"
              style={{
                borderRadius: "16px",
                fontSize: "20px",
                // paddingBlock: "7px",
                padding: "15px",
                // fontWeight: 700,
                width: "100%",
                minHeight: "56px",
                boxShadow: "0 8px 32px rgba(0,0,0,0.18)",
                letterSpacing: "0.01em",
                background: "rgba(0,0,0,0.12)",
                border: "1px solid rgba(255,255,255,0.1)",
                color: "var(--clr-light)",
                transition: "all 0.25s cubic-bezier(0.4,0,0.2,1)",
                backdropFilter: "blur(10px)",
                WebkitBackdropFilter: "blur(10px)",
                outline: "none",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                gap: "10px",
              }}
            >
              {t("interactiveMap.geoExplorer")}
              <span
                style={{
                  display: "inline-flex",
                  alignItems: "center",
                  fontSize: 22,
                  marginLeft: 6,
                }}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="20"
                  height="20"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                  strokeWidth="2"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M18 13v6a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6m5-3h3m0 0v6m0-6L10 14"
                  />
                </svg>
              </span>
            </button>
          </Link>
        </div>
      </div>
    </section>
  );
}
