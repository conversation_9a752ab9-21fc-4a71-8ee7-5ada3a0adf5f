import login_bg_image from "../assets/images/login_bg.png";
import login_wave_image from "../assets/images/login_wave.png";
import { useTranslation } from "react-i18next";
import { useEffect, useRef } from "react";

export default function SoonPage() {
  const { t } = useTranslation();

  return (
    <div className="login-page">
      <div
        className="login-bg"
        style={{ backgroundImage: `url(${login_bg_image})` }}
      ></div>
      <div
        className="login-wave"
        style={{ backgroundImage: `url(${login_wave_image})` }}
      ></div>
      {/* <div className="overlay"></div> */}
      <div className="container">
        <div style={{ padding: "40px 0", width: "800px", maxWidth: "100%" }}>
          <div
            className=""
            style={{
              height: "80vh",
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <div
              style={{
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                flex: 1,
              }}
            >
              <div
                style={{
                  display: "flex",
                  flexDirection: "column",
                  alignItems: "center",
                  gap: "20px",
                }}
              >
                <h1 style={{ color: "white", fontSize: "40px" }}>
                  {t("جاري الاطلاق...")}
                </h1>
                <AnimatedDots color="#a9593c" align="center" />
              </div>
            </div>
            {/* <div className="login-image-container">
              <img
                src={login_image}
                alt="login image"
                className="login-image"
              />
            </div> */}
          </div>

          <div className="footer-text">{t("footer.copyright")}</div>
        </div>
      </div>
    </div>
  );
}

type AnimatedDotsProps = {
  count?: number;
  size?: number;
  color?: string;
  gap?: number;
  speed?: number;
  align?: "left" | "center" | "right";
  ariaLabel?: string;
};

function AnimatedDots({
  count = 3,
  size = 10,
  color = "#111827", // gray-900
  gap = 6,
  speed = 1500,
  align = "left",
  ariaLabel = "Loading",
}: AnimatedDotsProps) {
  const dotsRef = useRef<HTMLSpanElement[]>([]);

  useEffect(() => {
    if (
      typeof window !== "undefined" &&
      window.matchMedia &&
      window.matchMedia("(prefers-reduced-motion: reduce)").matches
    ) {
      return;
    }

    dotsRef.current.forEach((el, i) => {
      if (!el?.animate) return;
      el.animate(
        [
          { opacity: 0.3, transform: "scale(0.8)" },
          { opacity: 1, transform: "scale(1.12)" },
          { opacity: 0.3, transform: "scale(0.8)" },
        ],
        {
          duration: speed,
          iterations: Infinity,
          easing: "ease-in-out",
          delay: (i * speed) / count,
        }
      );
    });
  }, [count, speed]);

  const justify =
    align === "center"
      ? "center"
      : align === "right"
      ? "flex-end"
      : "flex-start";

  return (
    <div
      role="status"
      aria-label={ariaLabel}
      style={{
        display: "flex",
        alignItems: "center",
        justifyContent: justify,
        gap: `${gap}px`,
      }}
    >
      {Array.from({ length: count }).map((_, i) => (
        <span
          key={i}
          ref={(el) => {
            if (el) dotsRef.current[i] = el;
          }}
          style={{
            width: `${size}px`,
            height: `${size}px`,
            backgroundColor: color,
            borderRadius: "9999px",
            display: "inline-block",
            willChange: "transform, opacity",
          }}
        />
      ))}
      {/* Accessible fallback text */}
      <span
        style={{
          position: "absolute",
          width: 1,
          height: 1,
          overflow: "hidden",
          clip: "rect(0 0 0 0)",
        }}
      >
        {ariaLabel}
      </span>
    </div>
  );
}
