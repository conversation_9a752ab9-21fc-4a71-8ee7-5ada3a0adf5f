import { Link } from "react-router";
import { urls } from "../../utils/urls";
import homeIcon from "../../assets/images/home-icon.svg";
import bg_image from "../../assets/images/breadcrumb_img.webp";
import { IoIosArrowBack } from "react-icons/io";
import { PropsWithChildren } from "react";

export default function Breadcrumb({
  children,
  bgImage,
}: PropsWithChildren<{ bgImage?: string }>) {
  return (
    <div className="breadcrumb">
      <img src={bgImage || bg_image} alt="" className="bg-image" />
      <div className="container-fluid content">
        <Link to={urls.home}>
          <img src={homeIcon} alt="" />
        </Link>
        <IoIosArrowBack />
        {children}
      </div>
    </div>
  );
}
