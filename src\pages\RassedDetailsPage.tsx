import { useTranslation } from "react-i18next";
import ServiceDetailsLayout from "../components/layouts/ServiceDetailsLayout";
import SectionTitle from "../components/common/SectionTitle";
import BeneficiariesList from "../components/services/BeneficiariesList";
import ServiceTimeSection from "../components/services/ServiceTimeSection";
import appleStore from "../assets/images/apple-store.png";
import googlePlay from "../assets/images/google-play.png";
import qrCode from "../assets/images/qr-code.svg";
import breadcrumbImg from "../assets/images/breadcrumb_img.webp";

export default function RassedDetailsPage() {
  const { t } = useTranslation();

  const mobileAppLinks = (
    <div
      className="mobile-app-links"
      style={{ flexDirection: "column", gap: "0", margin: "0" }}
    >
      <a href="#" target="_blank" className="store-link">
        <img src={appleStore} alt="Apple Store" style={{ width: "100px" }} />
      </a>
      <a href="#" target="_blank" className="store-link">
        <img src={googlePlay} alt="Google Play" style={{ width: "100px" }} />
      </a>
      <div className="qr-code">
        <img src={qrCode} alt="QR Code" style={{ width: "30px" }} />
      </div>
    </div>
  );

  const descriptionWithFeatures = (
    <>
      {t("rassedDetailsPage.description")}
      <ul className="section-list">
        {(
          t("rassedDetailsPage.features", {
            returnObjects: true,
          }) as string[]
        ).map((feature: string, index: number) => (
          <li key={index} className="section-description">
            {feature}
          </li>
        ))}
      </ul>
    </>
  );

  return (
    <ServiceDetailsLayout
      breadcrumbTitle={t("rassedDetailsPage.breadcrumb")}
      downloadGuideText={t("rassedDetailsPage.downloadGuide")}
      downloadCardContent={mobileAppLinks}
      breadcrumbBgImage={breadcrumbImg}
    >
      <SectionTitle
        title={t("rassedDetailsPage.title")}
        description={descriptionWithFeatures}
      />

      <div>
        <SectionTitle title={t("rassedDetailsPage.procedures.title")} />

        <ul className="section-list" style={{ width: "100%" }}>
          {(
            t("rassedDetailsPage.procedures.steps", {
              returnObjects: true,
            }) as string[]
          ).map((step: string, index: number) => (
            <li key={index} className="section-description">
              {step}
              {index === 2 && (
                <ul
                  className="section-list"
                  style={{
                    display: "grid",
                    gridTemplateColumns: "repeat(auto-fit, minmax(400px, 1fr))",
                    width: "100%",
                    gap: "20px",
                  }}
                >
                  {(
                    t("rassedDetailsPage.procedures.appFeatures", {
                      returnObjects: true,
                    }) as string[]
                  ).map((feature: string, idx: number) => (
                    <li key={idx} className="section-description">
                      {feature}
                    </li>
                  ))}
                </ul>
              )}
            </li>
          ))}
        </ul>
      </div>

      <BeneficiariesList
        title={t("rassedDetailsPage.beneficiaries.title")}
        beneficiaries={
          t("rassedDetailsPage.beneficiaries.list", {
            returnObjects: true,
          }) as string[]
        }
      />

      {/* <div>
        <SectionTitle
          title={t("rassedDetailsPage.terms.title")}
          description={t("rassedDetailsPage.terms.description")}
        />
      </div> */}

      <ServiceTimeSection
        title={t("rassedDetailsPage.serviceTime.title")}
        description={t("rassedDetailsPage.serviceTime.description")}
      />
    </ServiceDetailsLayout>
  );
}
