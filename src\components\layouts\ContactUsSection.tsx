import appleStore from "../../assets/images/apple-store.png";
import googlePlay from "../../assets/images/google-play.png";
import mobileAppMockup from "../../assets/images/phone.png";
import { motion } from "framer-motion";
import { useRef } from "react";
import { useTranslation } from "react-i18next";
import { QRCodeSVG } from "qrcode.react";

export default function ContactUsSection() {
  // Create refs for scroll animations
  const contactUsRef = useRef<HTMLElement>(null);
  const mobileAppRef = useRef<HTMLDivElement>(null);
  const mobileAppTitleRef = useRef<HTMLHeadingElement>(null);
  const mobileAppDescRef = useRef<HTMLParagraphElement>(null);
  const mobileAppLinksRef = useRef<HTMLDivElement>(null);
  const mobileAppImgRef = useRef<HTMLDivElement>(null);

  // Translation hooks
  const { t } = useTranslation();

  // Animation variants
  const fadeInUp = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut",
      },
    },
  };

  return (
    <section className="page-contact-us" ref={contactUsRef}>
      <section className="mbile-app">
        <div className="container-fluid">
          <div className="mbile-app-card">
            <div className="row g-4 align-items-center">
              <div className="col-12 col-lg-6 order-2 order-lg-0">
                <div className="mobile-app-info" ref={mobileAppRef}>
                  <motion.h4
                    className="title"
                    ref={mobileAppTitleRef}
                    initial="hidden"
                    whileInView="visible"
                    viewport={{ once: true, amount: 0.3 }}
                    variants={fadeInUp}
                  >
                    {t("footer.mobileApp.title")}
                  </motion.h4>
                  <motion.p
                    className="description"
                    ref={mobileAppDescRef}
                    initial="hidden"
                    whileInView="visible"
                    viewport={{ once: true, amount: 0.3 }}
                    variants={fadeInUp}
                    transition={{ delay: 0.2 }}
                  >
                    {t("footer.mobileApp.description")}
                  </motion.p>
                  <motion.div
                    className="mobile-app-links"
                    ref={mobileAppLinksRef}
                    initial="hidden"
                    whileInView="visible"
                    viewport={{ once: true, amount: 0.3 }}
                    variants={fadeInUp}
                    transition={{ delay: 0.3 }}
                  >
                    <div className="qr-code">
                      <QRCodeSVG
                        style={{ width: "45px", height: "45px" }}
                        value="https://reactjs.org/"
                      />
                    </div>
                    <a href="#" target="_blank" className="store-link">
                      <img
                        src={appleStore}
                        style={{ height: "45px" }}
                        alt="Apple Store"
                      />
                    </a>
                    <a href="#" target="_blank" className="store-link">
                      <img
                        src={googlePlay}
                        style={{ height: "45px" }}
                        alt="Google Play"
                      />
                    </a>
                    <div className="qr-code">
                      <QRCodeSVG
                        style={{ width: "45px", height: "45px" }}
                        value="https://reactjs.org/"
                      />
                    </div>
                  </motion.div>
                </div>
              </div>
              <div className="col-12 col-lg-6">
                <motion.div
                  className="mobile-img"
                  ref={mobileAppImgRef}
                  initial="hidden"
                  whileInView="visible"
                  viewport={{ once: true, amount: 0.3 }}
                  variants={fadeInUp}
                  transition={{ delay: 0.3 }}
                >
                  <img src={mobileAppMockup} alt="Mobile App Mockup" />
                </motion.div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </section>
  );
}
