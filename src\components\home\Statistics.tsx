import { useRef } from "react";
import { motion } from "framer-motion";
import StatisticsBg from "../../assets/images/statistics/statistics-bg.jpg";
import StatisticIcon1 from "../../assets/images/statistics/المناطق المحمية copy.png";
import StatisticIcon2 from "../../assets/images/statistics/التنوع الاحيائي.png";
import StatisticIcon3 from "../../assets/images/statistics/اعادة التوطين.png";
import StatisticIcon4 from "../../assets/images/statistics/النظم البيئية.png";
import StatisticIcon5 from "../../assets/images/statistics/statistic-icon-5.svg";
import StatisticIcon6 from "../../assets/images/statistics/المواقع الهامة.png";
import { useTranslation } from "react-i18next";
import CountUp from "../common/CountUp";
import {
  fadeIn,
  fadeInUp,
  staggerContainer,
  cardVariant,
} from "../../utils/animations";

const PortalStatistics = () => {
  const sectionRef = useRef(null);
  const { t } = useTranslation();

  return (
    <motion.section
      className="portal-statistics padding-block"
      ref={sectionRef}
      initial="hidden"
      animate="visible"
      variants={fadeIn}
    >
      <motion.div className="bg" variants={fadeIn}>
        <img src={StatisticsBg} alt="Statistics Background" />
      </motion.div>
      <div
        className="container-fluid"
        style={{
          position: "absolute",
          top: "50%",
          left: "50%",
          transform: "translate(-50%, -50%)",
          marginTop: "60px",
        }}
      >
        <div className="row g-3 align-items-center">
          <div className="col-12 col-lg-7" style={{ marginTop: 0 }}>
            <motion.div
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true, amount: 0.2 }}
              variants={fadeInUp(0)}
            >
              {/* <SectionTitle
                title={
                  t("statistics.title") + " " + t("statistics.titleHighlight")
                }
                coloredPart={t("statistics.titleHighlight")}
                description={t("statistics.description")}
                animationDelay={0.2}
              /> */}
              <div
                style={{ color: "#fff" }}
                className="section-header flex-column align-items-start"
              >
                <motion.h5
                  className="section-title"
                  initial="hidden"
                  animate="visible"
                  variants={fadeInUp(0.2)}
                  style={{ color: "#fff" }}
                >
                  {t("statistics.title") + " " + t("statistics.titleHighlight")}
                </motion.h5>
                <motion.div
                  className="section-description"
                  initial="hidden"
                  animate="visible"
                  variants={fadeInUp(0.4)}
                >
                  {t("statistics.description")}
                </motion.div>
              </div>
            </motion.div>
          </div>
        </div>
        <div className="row g-3 align-items-center mb-4">
          <div className="col-12 col-lg-7">
            <motion.div
              className="row g-3"
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true, amount: 0.2 }}
              variants={staggerContainer}
            >
              {[
                StatisticIcon3,
                StatisticIcon2,
                StatisticIcon1,
                StatisticIcon4,
                StatisticIcon6,
                StatisticIcon5,
              ].map((icon, index) => (
                <motion.div
                  key={index}
                  className="col-6 col-lg-4"
                  variants={cardVariant}
                  transition={{ delay: index * 0.1 }}
                >
                  <div className="statistic-card">
                    <span className="statistic-icon">
                      <img src={icon} alt={`Statistic Icon ${index + 1}`} />
                    </span>
                    <span className="statistic-count">
                      {index === 0 && (
                        <CountUp
                          from={0}
                          to={8700}
                          className="count-to"
                          delay={0.1 + index * 0.1}
                        />
                      )}
                      {index === 1 && (
                        <CountUp
                          from={0}
                          to={12000}
                          className="count-to"
                          delay={0.1 + index * 0.1}
                        />
                      )}
                      {index === 2 && (
                        <CountUp
                          from={0}
                          to={36}
                          className="count-to"
                          delay={0.1 + index * 0.1}
                        />
                      )}
                      {index === 3 && (
                        <CountUp
                          from={0}
                          to={107}
                          className="count-to"
                          delay={0.1 + index * 0.1}
                        />
                      )}
                      {index === 4 && (
                        <CountUp
                          from={0}
                          to={556}
                          className="count-to"
                          delay={0.1 + index * 0.1}
                        />
                      )}
                      {index === 5 && (
                        <CountUp
                          from={0}
                          to={0}
                          className="count-to"
                          delay={0.1 + index * 0.1}
                        />
                      )}
                    </span>
                    <span className="statistic-title">
                      {
                        [
                          t("performanceIndicators.indicators.resettlement"),
                          t("performanceIndicators.indicators.biodiversity"),
                          t("performanceIndicators.indicators.protectedAreas"),
                          t("performanceIndicators.indicators.wetlands"),
                          t("performanceIndicators.indicators.importantSites"),
                          t(
                            "performanceIndicators.indicators.monitoringObservations"
                          ),
                        ][index]
                      }
                    </span>
                  </div>
                </motion.div>
              ))}
            </motion.div>
          </div>
        </div>
      </div>
    </motion.section>
  );
};

export default PortalStatistics;
