import { useTranslation } from "react-i18next";
import PageLayout from "../components/layouts/PageLayout";
import { motion } from "framer-motion";
import { cardVariant } from "../utils/animations";

export default function AboutPage() {
  const { t } = useTranslation();

  const tasks = Array.from({ length: 11 }, (_, i) => ({
    id: i,
    text: t(`aboutManagement.tasks.items.${i}`),
  }));

  return (
    <PageLayout breadcrumbTitle={t("navbar.about")} backgroundAlt="Map Vector">
      {/* Hero Section -------------------------------------------------- */}
      <section style={hero.wrapper}>
        <div style={hero.container}>
          <motion.div
            style={hero.glass}
            initial={{ scale: 0.95, opacity: 0, y: 30 }}
            animate={{ scale: 1, opacity: 1, y: 0 }}
            transition={{ duration: 0.8, ease: [0.25, 0.46, 0.45, 0.94] }}
          >
            <motion.h1
              style={hero.title}
              initial={{ y: 40, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.1 }}
            >
              {t("aboutManagement.managementTitle.data")}{" "}
              <span style={hero.accent}>
                {t("aboutManagement.managementTitle.geo")}
              </span>
            </motion.h1>

            <motion.p
              style={hero.desc}
              initial={{ y: 40, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              {t("aboutManagement.managementDescription")}
            </motion.p>
          </motion.div>
        </div>
      </section>

      {/* Tasks Section ------------------------------------------------- */}
      <section style={tasksSection.wrapper}>
        <div style={tasksSection.inner}>
          <motion.div
            style={tasksSection.header}
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
          >
            <motion.div
              style={hero.badge}
              initial={{ opacity: 0, scale: 0.9 }}
              whileInView={{ opacity: 1, scale: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.1 }}
            >
              {t("aboutManagement.capabilitiesBadge")}
            </motion.div>

            <h2 style={tasksSection.title}>
              {t("aboutManagement.tasks.title.tasks")}{" "}
              <span style={tasksSection.accent}>
                {t("aboutManagement.tasks.title.management")}
              </span>
            </h2>
          </motion.div>

          <motion.div
            style={tasksSection.grid}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, amount: 0.2 }}
            transition={{ staggerChildren: 0.08 }}
          >
            {tasks.map(({ id, text }, index) => (
              <motion.div
                key={id}
                variants={cardVariant}
                style={{
                  ...taskCard.wrapper,
                  animationDelay: `${index * 0.1}s`,
                }}
                whileHover={{
                  y: -12,
                  scale: 1.03,
                  boxShadow:
                    "0 25px 50px rgba(0, 0, 0, 0.08), 0 0 0 1px rgba(180, 84, 52, 0.1)",
                  transition: { duration: 0.3, ease: "easeOut" },
                }}
                whileTap={{ scale: 0.97 }}
              >
                {/* Gradient Background */}
                <div style={taskCard.gradientBg}></div>

                {/* Content Container */}
                <div style={taskCard.content}>
                  {/* Icon Container */}
                  <div style={taskCard.iconContainer}>
                    <div style={taskCard.iconWrapper}>
                      <span style={taskCard.number}>
                        {String(id + 1).padStart(2, "0")}
                      </span>
                    </div>
                    {/* Decorative dots */}
                    {/* <div style={taskCard.dots}>
                      <span style={taskCard.dot}></span>
                      <span style={taskCard.dot}></span>
                      <span style={taskCard.dot}></span>
                    </div> */}
                  </div>

                  {/* Text Content */}
                  <div style={taskCard.textContent}>
                    <p style={taskCard.text}>{text}</p>

                    {/* Bottom accent line */}
                    <div style={taskCard.accentLine}></div>
                  </div>
                </div>

                {/* Corner decoration */}
                <div style={taskCard.cornerDecoration}></div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>
    </PageLayout>
  );
}

/* ---------- Style Objects ---------- */
const hero = {
  wrapper: {
    paddingBlock: "5rem",
    background: `
      radial-gradient(ellipse at top, rgba(180, 84, 52, 0.05) 0%, transparent 50%),
      radial-gradient(ellipse at bottom right, rgba(59, 130, 246, 0.03) 0%, transparent 50%),
      linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)
    `,
    position: "relative" as const,
    overflow: "hidden",
  },
  container: {
    maxWidth: "1200px",
    margin: "0 auto",
    padding: "0 clamp(1.2rem, 4vw, 2rem)",
  },
  glass: {
    padding: "clamp(3rem, 6vw, 4.5rem) clamp(2rem, 5vw, 3.5rem)",
    borderRadius: "32px",
    backdropFilter: "blur(24px) saturate(180%)",
    backgroundColor: "rgba(255, 255, 255, 0.7)",
    border: "1px solid rgba(255, 255, 255, 0.5)",
    boxShadow: `
      0 32px 64px rgba(0, 0, 0, 0.06),
      0 8px 32px rgba(0, 0, 0, 0.04),
      inset 0 1px 0 rgba(255, 255, 255, 0.8)
    `,
    textAlign: "center" as const,
    position: "relative" as const,
  },
  badge: {
    display: "inline-block",
    padding: "0.5rem 1.25rem",
    backgroundColor: "rgba(180, 84, 52, 0.1)",
    color: "#b45434",
    borderRadius: "50px",
    fontSize: "0.875rem",
    fontWeight: 600,
    marginBottom: "1rem",
    border: "1px solid rgba(180, 84, 52, 0.2)",
  },
  title: {
    fontSize: "clamp(2.5rem, 6vw, 4rem)",
    fontWeight: 900,
    color: "#0f172a",
    lineHeight: 1.1,
    marginBottom: "1.5rem",
    background: "linear-gradient(135deg, #0f172a 0%, #1e293b 100%)",
    WebkitBackgroundClip: "text",
    WebkitTextFillColor: "transparent",
  },
  accent: {
    WebkitBackgroundClip: "text",
    WebkitTextFillColor: "transparent",
  },
  desc: {
    fontSize: "clamp(1.1rem, 2.8vw, 1.375rem)",
    color: "#475569",
    maxWidth: "60ch",
    margin: "0 auto 3rem",
    lineHeight: 1.7,
    fontWeight: 400,
  },
};

const tasksSection = {
  wrapper: {
    paddingBlock: "5rem",
    background: `
      radial-gradient(circle at 20% 20%, rgba(180, 84, 52, 0.03) 0%, transparent 50%),
      radial-gradient(circle at 80% 80%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),
      linear-gradient(180deg, #f8fafc 0%, #ffffff 100%)
    `,
  },
  inner: {
    maxWidth: "1200px",
    margin: "0 auto",
    padding: "0 clamp(1.2rem, 4vw, 2rem)",
  },
  header: {
    textAlign: "center" as const,
    marginBottom: "4rem",
  },
  title: {
    fontSize: "clamp(2rem, 5vw, 3.25rem)",
    fontWeight: 800,
    // color: "#0f172a",
    color: "#b45434",
    lineHeight: 1.2,
  },
  accent: {
    color: "#b45434",
    fontWeight: 600,
  },
  grid: {
    display: "grid",
    gridTemplateColumns: "repeat(auto-fit, minmax(250px, 1fr))",
    gap: "1.75rem",
  },
};

const taskCard = {
  wrapper: {
    position: "relative" as const,
    borderRadius: "20px",
    overflow: "hidden",
    cursor: "pointer",
    transition: "all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94)",
    border: "1px solid rgba(255, 255, 255, 0.2)",
    boxShadow: `
      0 4px 20px rgba(0, 0, 0, 0.03),
      0 1px 3px rgba(0, 0, 0, 0.05),
      inset 0 1px 0 rgba(255, 255, 255, 0.8)
    `,
  },
  gradientBg: {
    position: "absolute" as const,
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: `
      linear-gradient(135deg, 
        rgba(255, 255, 255, 0.95) 0%,
        rgba(255, 255, 255, 0.85) 50%,
        rgba(180, 84, 52, 0.02) 100%
      )
    `,
    backdropFilter: "blur(20px) saturate(180%)",
  },
  content: {
    position: "relative" as const,
    zIndex: 2,
    padding: "1.5rem",
    height: "100%",
    display: "flex",
    flexDirection: "column" as const,
  },
  iconContainer: {
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: "1.5rem",
  },
  iconWrapper: {
    width: "56px",
    height: "56px",
    borderRadius: "18px",
    background: `
      linear-gradient(135deg, 
        #b45434 0%, 
        #a0472e 50%, 
        #8b3e26 100%
      )
    `,
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    boxShadow: `
      0 8px 25px rgba(180, 84, 52, 0.25),
      0 3px 10px rgba(180, 84, 52, 0.15),
      inset 0 1px 0 rgba(255, 255, 255, 0.2)
    `,
    position: "relative" as const,
  },
  number: {
    fontSize: "1.25rem",
    fontWeight: 800,
    color: "white",
    textShadow: "0 1px 2px rgba(0, 0, 0, 0.1)",
  },
  dots: {
    display: "flex",
    gap: "4px",
    alignItems: "center",
  },
  dot: {
    width: "4px",
    height: "4px",
    borderRadius: "50%",
    backgroundColor: "rgba(180, 84, 52, 0.3)",
    display: "block",
  },
  textContent: {
    flex: 1,
    display: "flex",
    flexDirection: "column" as const,
    position: "relative" as const,
  },
  text: {
    margin: 0,
    fontSize: "1rem",
    lineHeight: 1.65,
    color: "#374151",
    fontWeight: 400,
    flex: 1,
  },
  accentLine: {
    width: "40px",
    height: "3px",
    background:
      "linear-gradient(90deg, #b45434 0%, rgba(180, 84, 52, 0.3) 100%)",
    borderRadius: "2px",
    marginTop: "1.25rem",
    transition: "width 0.3s ease",
  },
  cornerDecoration: {
    position: "absolute" as const,
    top: "16px",
    right: "16px",
    width: "24px",
    height: "24px",
    background: `
      radial-gradient(circle at center, 
        rgba(180, 84, 52, 0.1) 0%, 
        rgba(180, 84, 52, 0.05) 50%, 
        transparent 70%
      )
    `,
    borderRadius: "50%",
    zIndex: 1,
  },
};
