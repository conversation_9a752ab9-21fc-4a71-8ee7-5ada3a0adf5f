import { oneGuide<PERSON>ist<PERSON>tom } from "../../atoms/guides/one-guide-list-atom";
import { useEffect } from "react";
import Packery from "packery";
import imagesLoaded from "imagesloaded";

// import guide_details from "../../assets/images/breadcrumb_img.webp";
import { selectedGuideIdAtom } from "../../atoms/guides/selected-guide-id-atom";
import { currentPageAtom } from "../../atoms/pagination/current-page-atom";
import { getOneGuideList } from "../../services/get-one-guide-list";
import { selectedGuideAtom } from "../../atoms/guides/selected-guide";
import { scrollToTop } from "../../utils/helpers";
import { Link } from "react-router";
import { TbWorldShare } from "react-icons/tb";
import i18n from "../../locales/i18n";

// const placeholderImage =
//   "https://geoservices2.syadtech.com/wildlifefiles/uploads/Mammals/Buhidae-sp/1.jpg";

// types/packery.d.ts
declare module "packery" {
  export default class Packery {
    constructor(element: Element, options?: unknown);
    layout(): void;
    destroy(): void;
  }
}

export default function OneGuideList() {
  const { data, isLoading } = oneGuideListAtom.useValue();

  const { pagination } = oneGuideListAtom.useValue();

  const { guide } = selectedGuideAtom.useValue();

  useEffect(() => {
    const grid = document.querySelector(".grid");
    if (!grid) return;
    if (!grid) return;
    const pckry = new Packery(grid, {
      percentPosition: true,
    });

    // layout Packery after each image loads
    imagesLoaded(grid).on("progress", () => {
      pckry.layout();
    });

    return () => {
      // Cleanup if needed
      pckry.destroy();
    };
  }, []);

  const currentPage = currentPageAtom.useValue();

  const handleClick = () => {
    if (currentPage < pagination - 1) {
      currentPageAtom.update(currentPage + 1);
    }

    getOneGuideList(guide?.id as number, currentPage + 1);

    scrollToTop();
  };

  if (isLoading) {
    return (
      <div className="container-fluid" style={{ marginBlock: "100px" }}>
        <p
          style={{
            textAlign: "center",
            fontSize: "20px",
            marginBlock: "100px",
          }}
        >
          جاري التحميل...
        </p>
      </div>
    );
  }

  // if (data.length === 0) {
  //   return (
  //     <div className="container-fluid" style={{ marginBlock: "100px" }}>
  //       <p
  //         style={{
  //           textAlign: "center",
  //           fontSize: "20px",
  //           marginBlock: "100px",
  //         }}
  //       >
  //         لا يوجد نتائج
  //       </p>
  //     </div>
  //   );
  // }

  return (
    <>
      <div className="container-fluid" style={{ marginBlock: "100px" }}>
        <div className="one-guide-list">
          {data.map(
            ({ media_path, id, name_en, reference_title }) => {
              return (
                <div
                  className="one-guide-item"
                  key={id}
                  // data-bs-toggle="modal"
                  // data-bs-target="#exampleModal"
                  // onClick={() => {
                  //   selectedGuideIdAtom.change("guideId", id);
                  // }}
                >
                  <img
                    className="guide-img"
                    src={
                      !media_path
                        ? `${window.domain}/wildlifefiles/${guide?.img_path}`
                        : !media_path.includes("/")
                        ? `${window.domain}/wildlifefiles/${guide?.img_path}`
                        : media_path.startsWith("http")
                        ? media_path
                        : media_path.includes(",")
                        ? `${window.domain}/wildlifefiles/${
                            media_path.split(",")[0]
                          }`
                        : `${window.domain}/wildlifefiles/${media_path}`
                    }
                    alt={name_en || "Wildlife image"}
                    onError={(e) => {
                      e.currentTarget.onerror = null;
                      e.currentTarget.src = `${window.domain}/wildlifefiles/${guide?.img_path}`;
                    }}
                    data-bs-toggle="modal"
                    data-bs-target="#exampleModal"
                    onClick={() => {
                      selectedGuideIdAtom.change("guideId", id);
                    }}
                  />

                  <div className="overlay">
                    <div
                      style={{
                        // borderBottom: "1px solid #fff",
                        paddingBlock: "10px",
                        fontSize: "16px",
                      }}
                    >
                      {name_en}
                    </div>

                    <Link
                      // to={reference_url}
                      to={""}
                      // className="flex items-center gap-2 text-[14px] text-[#707070]"
                      style={{
                        fontSize: "14px",
                        display: "flex",
                        alignItems: "center",
                        gap: "5px",
                        color: "#b45434",
                      }}
                      // target="_blank"
                    >
                      <TbWorldShare />
                      {reference_title}
                    </Link>
                    {/* <div
                    style={{
                      fontSize: "12px",
                      display: "flex",
                      alignItems: "center",
                      gap: "5px",
                    }}
                  >
                    <IoLocationOutline />
                    ثيللا - غبار - جدة
                  </div> */}
                  </div>
                </div>
              );
            }
          )}
        </div>

        {data.length !== 0 && (
          <button
            onClick={handleClick}
            className="more-btn"
            disabled={currentPage >= pagination - 1}
          >
            {i18n.language === "ar" ? "المزيد" : "Load More"}
          </button>
        )}
      </div>
    </>
  );
}
