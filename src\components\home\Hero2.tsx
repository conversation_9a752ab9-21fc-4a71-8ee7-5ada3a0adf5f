import { useTranslation } from "react-i18next";
import { Swiper, SwiperSlide } from "swiper/react";
import { Pagination, Autoplay } from "swiper/modules";
import { useRef, useState } from "react";

import slide_1 from "../../assets/images/hero/1.webp";
import slide_2 from "../../assets/images/hero/2.webp";
import slide_3 from "../../assets/images/hero/3.webp";
import slide_4 from "../../assets/images/hero/4.webp";

export default function Hero2() {
  const { t } = useTranslation();
  const slides = [
    {
      bg: slide_1,
      title: t("hero2.slide1.title"),
      desc: t("hero2.slide1.desc"),
      button: t("heroSection.learnMore"),
    },
    {
      bg: slide_2,
      title: t("hero2.slide2.title"),
      desc: t("hero2.slide2.desc"),
      button: t("heroSection.learnMore"),
    },
    {
      bg: slide_3,
      title: t("hero2.slide3.title"),
      desc: t("hero2.slide3.desc"),
      button: t("heroSection.learnMore"),
    },
    {
      bg: slide_4,
      title: t("hero2.slide4.title"),
      desc: t("hero2.slide4.desc"),
      button: t("heroSection.learnMore"),
    },
  ];

  const [activeIndex, setActiveIndex] = useState(0);
  const swiperRef = useRef<any>(null);

  const handleBulletClick = (idx: number) => {
    if (swiperRef.current && swiperRef.current.swiper) {
      swiperRef.current.swiper.slideTo(idx);
    }
  };

  return (
    <section
      style={{
        minHeight: "100vh",
        width: "100vw",
        overflow: "hidden",
        padding: 0,
        margin: 0,
      }}
    >
      {/* Custom style for bar pagination */}
      <style>{`
        .hero2-slider-bar-pagination {
          position: absolute;
          left: 0;
          right: 0;
          bottom: 120px;
          display: flex;
          gap: 16px;
          align-items: center;
          justify-content: center;
          z-index: 10;
        }
        .hero2-slider-bar-pagination .bar {
          height: 8px;
          width: 40px;
          border-radius: 4px;
          background: #e0e0e0;
          opacity: 0.7;
          cursor: pointer;
          transition: all 0.3s cubic-bezier(.4,2,.6,1);
        }
        .hero2-slider-bar-pagination .bar.active {
          width: 64px;
          background: var(--clr-primary, #b45434);
          opacity: 1;
        }
      `}</style>
      <Swiper
        ref={swiperRef}
        modules={[
          Pagination,
          Autoplay,
          //  EffectFade
        ]}
        // effect="fade"
        autoplay={{ delay: 6000, disableOnInteraction: false }}
        observeParents={true}
        className="hero2-slider"
        style={{ width: "100vw", height: "100vh" }}
        onSlideChange={(swiper) => setActiveIndex(swiper.activeIndex)}
      >
        {slides.map((slide, idx) => (
          <SwiperSlide key={idx}>
            <div
              style={{
                minHeight: "100vh",
                width: "100vw",
                background: `linear-gradient(180deg,rgba(0,0,0,0.6) 0%,rgba(0,0,0,0.4) 60%,rgba(0,0,0,0.7) 100%), url(${slide.bg}) center/cover no-repeat`,
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                position: "relative",
              }}
            >
              <div
                style={{
                  zIndex: 2,
                  textAlign: "center",
                  // maxWidth: 900,
                  margin: "0 auto",
                  color: "#fff",
                  padding: "0 1.5rem",
                  width: "100%",
                  display: "flex",
                  flexDirection: "column",
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                <h1
                  style={{
                    fontSize: "3rem",
                    fontWeight: 800,
                    marginBottom: 24,
                    lineHeight: 1.2,
                    letterSpacing: "-1px",
                  }}
                >
                  {slide.title}
                </h1>
                <p
                  style={{
                    fontSize: "1.25rem",
                    marginBottom: 40,
                    fontWeight: 400,
                    lineHeight: 1.7,
                  }}
                >
                  {slide.desc}
                </p>
              </div>
            </div>
          </SwiperSlide>
        ))}
      </Swiper>
      {/* Bar style pagination */}
      <div className="hero2-slider-bar-pagination">
        {[0, 1, 2, 3].map((idx) => (
          <span
            key={idx}
            className={"bar" + (activeIndex === idx ? " active" : "")}
            onClick={() => handleBulletClick(idx)}
          />
        ))}
      </div>
    </section>
  );
}
