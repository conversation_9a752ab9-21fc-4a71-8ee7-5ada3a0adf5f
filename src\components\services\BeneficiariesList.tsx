import { motion } from "framer-motion";
import { staggerContainer, cardVariant } from "../../utils/animations";
import SectionTitle from "../common/SectionTitle";
// import logo from "../../assets/images/e-services/explorer-logo.svg";

interface BeneficiariesListProps {
  title: string;
  coloredPart?: string;
  beneficiaries: string[];
  className?: string;
}

/**
 * A reusable component for displaying beneficiaries lists in service pages
 */
const BeneficiariesList: React.FC<BeneficiariesListProps> = ({
  title,
  coloredPart,
  beneficiaries,
  className = "",
}) => {
  return (
    <div className={className}>
      <SectionTitle title={title} coloredPart={coloredPart} />

      <motion.div
        className="explorer-list"
        initial="hidden"
        animate="visible"
        variants={staggerContainer}
      >
        {beneficiaries.map((beneficiary, index) => (
          <motion.div
            className=""
            key={index}
            variants={cardVariant}
            transition={{ delay: index * 0.05 }}
          >
            <div className="explorer-card">
              {/* <img src={logo} alt="" /> */}
              <span>{beneficiary}</span>
            </div>
          </motion.div>
        ))}
      </motion.div>
    </div>
  );
};

export default BeneficiariesList;
