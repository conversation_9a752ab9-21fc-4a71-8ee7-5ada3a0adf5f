import { useEffect, useState } from "react";

export default function useSmallScreen(size: number) {
  const [isSmallScreen, setIsSmallScreen] = useState(window.innerWidth <= size);

  useEffect(() => {
    const handleResize = () => {
      setIsSmallScreen(window.innerWidth <= size);
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  return {
    isSmallScreen,
  };
}
