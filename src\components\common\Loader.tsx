import { useLocation } from "react-router";
import logo from "../../assets/images/logo.svg";
import { useEffect } from "react";

interface LoaderProps {
  isInfinteLoad?: boolean;
}

export default function Loader(props: LoaderProps) {
  const { pathname } = useLocation();

  useEffect(() => {
    const preloader = document.getElementById("preloader");
    const content = document.querySelector(".preloader-content");
    const slices = document.querySelectorAll(".slice");

    if (!props.isInfinteLoad) {
      // If page is already loaded (e.g. coming back to Home via router), hide the preloader
      if (document.readyState === "complete") {
        if (preloader) preloader.style.display = "none";
        document.body.style.overflow = "auto";
        return;
      }
    }

    function animatePreloaderOut() {
      if (content) content.classList.add("fade-out");

      setTimeout(() => {
        if (slices[0]) slices[0].classList.add("slide1");
        if (slices[1]) slices[1].classList.add("slide2");
      }, 600);

      setTimeout(() => {
        if (preloader) preloader.style.display = "none";
        document.body.style.overflow = "auto";
      }, 1400);
    }

    window.addEventListener("load", animatePreloaderOut);

    return () => {
      window.removeEventListener("load", animatePreloaderOut);
    };
  }, []);

  return (
    <div id="preloader">
      <div className="preloader-content">
        <div className="logo">
          <img src={logo} alt="" />
        </div>
        {pathname.includes("login") ? (
          <h1>جاري تسجيل الدخول ...</h1>
        ) : (
          <h1>جارِ التحميل ...</h1>
        )}
      </div>
      <div className="slice slice1"></div>
      <div className="slice slice2"></div>
    </div>
  );
}
