import { ReactNode, useEffect } from "react";
import Breadcrumb from "../common/Breadcrumb";
import page_bg from "../../assets/images/page_bgg.png";
import { scrollToTop } from "../../utils/helpers";
import { motion } from "framer-motion";
import { fadeIn } from "../../utils/animations";

interface PageLayoutProps {
  children: ReactNode;
  breadcrumbTitle?: string;
  breadcrumbContent?: ReactNode;
  backgroundAlt?: string;
  scrollToTopOnMount?: boolean;
  className?: string;
  breadcrumbBgImage?: string;
}

/**
 * A reusable page layout component that includes common elements like breadcrumb and background
 */
const PageLayout: React.FC<PageLayoutProps> = ({
  children,
  breadcrumbTitle,
  breadcrumbContent,
  backgroundAlt = "Background Image",
  scrollToTopOnMount = true,
  className = "",
  breadcrumbBgImage,
}) => {
  useEffect(() => {
    if (scrollToTopOnMount) {
      scrollToTop();
    }
  }, [scrollToTopOnMount]);

  return (
    <div className={`e-services ${className}`}>
      <Breadcrumb bgImage={breadcrumbBgImage}>
        {breadcrumbContent || (breadcrumbTitle && <h2>{breadcrumbTitle}</h2>)}
      </Breadcrumb>

      <motion.div
        className="bg"
        initial="hidden"
        animate="visible"
        variants={fadeIn}
      >
        <img src={page_bg} alt={backgroundAlt} />
      </motion.div>

      {children}
    </div>
  );
};

export default PageLayout;
