import { useEffect } from "react";
import { Route, Routes } from "react-router";

import HomePage from "./pages/HomePage";
import NotFoundPage from "./pages/NotFoundPage";
import AboutPage from "./pages/AboutPage";
import ServicesPage from "./pages/ServicesPage";
import ExplorerDetailsPage from "./pages/ExplorerDetailsPage";
import GuidesPage from "./pages/GuidesPage";
import RassedDetailsPage from "./pages/RassedDetailsPage";
import RequestDataDetailsPage from "./pages/RequestDataDetailsPage";
import MakeDecisionDetailsPage from "./pages/MakeDecisionDetailsPage";
import LoginSso from "./pages/LoginSso";
import LoginPage from "./pages/LoginPage";
import AppsPage from "./pages/AppsPage";

import BackToTop from "./components/common/BackToTop";
import Loader from "./components/common/Loader"; // You can remove this if not used anymore
import BaseLayout from "./components/layouts/BaseLayout";
import { Toaster } from "react-hot-toast";
import { useTranslation } from "react-i18next";
import { locale_atom } from "./atoms/locale-atom";
import Logout from "./pages/Logout";
import SoonPage from "./pages/SoonPage";

export default function App() {
  const { i18n } = useTranslation();
  const locale_code = locale_atom.useValue();

  useEffect(() => {
    i18n.changeLanguage(locale_code);
  }, [locale_code]);

  return (
    <div dir={locale_code === "ar" ? "rtl" : "ltr"}>
      <Routes>
        <Route path="/" element={<HomePage />} />

        <Route path="/" element={<BaseLayout />}>
          <Route path="about" element={<AboutPage />} />
          <Route path="my-apps" element={<AppsPage />} />
          <Route path="loginsso" element={<LoginSso />} />
          <Route path="services" element={<ServicesPage />} />
          <Route path="services/explorer" element={<ExplorerDetailsPage />} />
          <Route path="services/rassed" element={<RassedDetailsPage />} />
          <Route
            path="services/request-data"
            element={<RequestDataDetailsPage />}
          />
          <Route
            path="services/make-decision"
            element={<MakeDecisionDetailsPage />}
          />
          <Route path="guides" element={<GuidesPage />} />
        </Route>

        <Route path="/login" element={<LoginPage />} />
        <Route path="/soon" element={<SoonPage />} />
        <Route path="*" element={<NotFoundPage />} />
        <Route path="/logout" element={<Logout />} />
      </Routes>

      <BackToTop />
      <Toaster position="top-center" reverseOrder={false} />
      <Loader />
    </div>
  );
}
