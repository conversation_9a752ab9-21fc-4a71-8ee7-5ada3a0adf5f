import Breadcrumb from "../components/common/Breadcrumb";
import page_bg from "../assets/images/page_bgg.png";

import Icon2 from "../assets/images/e-services/الراصد.svg";
import Icon1 from "../assets/images/e-services/المستكشف.svg";
import Icon3 from "../assets/images/e-services/طلب خرائط و بيانات.svg";
import Icon4 from "../assets/images/e-services/اتخاذ القرار.svg";
import Icon5 from "../assets/images/e-services/icon-5.svg";
import Icon6 from "../assets/images/e-services/التكامل.svg";
import Icon7 from "../assets/images/e-services/البيانات المفتوحة.svg";
import Icon8 from "../assets/images/e-services/icon-8.svg";
import Icon9 from "../assets/images/e-services/icon-9.svg";
import Icon10 from "../assets/images/e-services/الدراسات الميدانية.png";
import Icon11 from "../assets/images/e-services/المخالفات في المحميات.svg";
import Icon12 from "../assets/images/e-services/icon-12.svg";

import { Link } from "react-router";
import { useEffect } from "react";
import { motion } from "framer-motion";
import { useTranslation } from "react-i18next";
import SectionTitle from "../components/common/SectionTitle";

// Animation variants
const fadeIn = {
  hidden: { opacity: 0 },
  visible: { opacity: 1, transition: { duration: 0.8 } },
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const cardVariant = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.5,
      ease: "easeOut",
    },
  },
};

export default function ServicesPage() {
  const { t } = useTranslation();

  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  // Service icons array
  const serviceIcons = [
    Icon1, // Wildlife Rasid   Explorer
    Icon3, // Maps and Data Request
    Icon2, // Wildlife Monitor
    Icon4, // Decision Support
    Icon5, // Data Analysis
    Icon6, // Integration
    Icon7, // Open Data
    Icon8, // Data Update
    Icon9, // Documents
    Icon10, // Field Studies
    Icon11, // Violations in Protected Areas
    Icon12, // User Management
  ];

  // Service links array
  const serviceLinks = [
    "/services/explorer",
    "/services/request-data",
    "/services/rassed",
    "/services/make-decision",
    "#",
    "#",
    "#",
    "#",
    "#",
    "#",
    "#",
    "#",
  ];

  // Get service items from translations
  const serviceItems = t("services.serviceItems", {
    returnObjects: true,
  }) as Array<{
    name: string;
    text: string;
  }>;

  return (
    <div className="e-services">
      <Breadcrumb>
        <h2>{t("services.breadcrumb")}</h2>
      </Breadcrumb>

      <motion.div
        className="bg"
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, amount: 0.1 }}
        variants={fadeIn}
      >
        <img src={page_bg} alt="Map Vector" />
      </motion.div>

      <div className="container-fluid">
        <SectionTitle
          // title={t("services.title") + " " + t("services.titleHighlight")}
          title=""
          description={t("services.description")}
        />
      </div>

      {/*  */}
      <div className="container-fluid services-list">
        <div className="row g-3 align-items-center mb-4">
          <div className="col-12">
            <motion.div
              className="row g-4"
              initial="hidden"
              animate="visible"
              variants={staggerContainer}
            >
              {serviceItems.map((service, index) => {
                return (
                  <motion.div
                    className="col-12 col-md-6 col-lg-3"
                    key={index}
                    variants={cardVariant}
                    transition={{ delay: index * 0.05 }}
                  >
                    <Link
                      to={serviceLinks[index]}
                      style={{ height: "100%", display: "block" }}
                    >
                      <div className="services-card">
                        <span className="services-icon">
                          <img src={serviceIcons[index]} alt="" />
                        </span>
                        <span className="card-title">{service.name}</span>
                        <span className="services-title">{service.text}</span>
                      </div>
                    </Link>
                  </motion.div>
                );
              })}
            </motion.div>
          </div>
        </div>
      </div>
      {/*  */}
    </div>
  );
}
