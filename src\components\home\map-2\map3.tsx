import React, { useCallback, useMemo, useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { Link } from "react-router-dom";

/* 1. Assets */
import bg1 from "../../../assets/images/ncw-map/bg1.webp";
import bg2 from "../../../assets/images/ncw-map/bg2.webp";
import img2017 from "../../../assets/images/ncw-map/2017.webp";
import img2025 from "../../../assets/images/ncw-map/2025.webp";
import img2030 from "../../../assets/images/ncw-map/2030.webp";
import noBorder from "../../../assets/images/ncw-map/بلا حدود.webp";

import p1 from "../../../assets/images/ncw-map/أشجار و مناطق الغابات الجبلية.webp";
import p2 from "../../../assets/images/ncw-map/البحر الاحمر.webp";
import p3 from "../../../assets/images/ncw-map/الخليج العربي.webp";
import p4 from "../../../assets/images/ncw-map/الرمال العربية.webp";
import p5 from "../../../assets/images/ncw-map/السهل الساحلي الغربي.webp";
import p6 from "../../../assets/images/ncw-map/السهل الساحلي للخليج العربي.webp";
import p7 from "../../../assets/images/ncw-map/المرتفعات الغربية.webp";
import p8 from "../../../assets/images/ncw-map/الهضاب العربية.webp";
import p9 from "../../../assets/images/ncw-map/صحراء المنطقة الشمالية.webp";

import pb1 from "../../../assets/images/ncw-map/الغابات الجبلية.webp";
import pb2 from "../../../assets/images/ncw-map/2121123.webp";
import pb3 from "../../../assets/images/ncw-map/bg3.webp";
import pb4 from "../../../assets/images/ncw-map/الكثبان الرملية.webp";
import pb5 from "../../../assets/images/ncw-map/السهل الساحلي الغربي (1).webp";
import pb6 from "../../../assets/images/ncw-map/السهل الساحلي الشرقي.webp";
import pb7 from "../../../assets/images/ncw-map/المرتفعات الجبلية.webp";
import pb8 from "../../../assets/images/ncw-map/هضاب2.webp";
import pb9 from "../../../assets/images/ncw-map/الصحراء الشمالية.webp";

/* 2. Data */

// Type for stats
interface Stats {
  [key: string]: { sea: string; land: string };
}

const stats: Stats = {
  "2017": { sea: "3%", land: "3%" },
  "2025": { sea: "24%", land: "22%" },
  "2030": { sea: "30%", land: "30%" },
};

// Type for legend items
interface LegendItem {
  color: string;
  key: string;
  mapSrc: string;
  bgSrc: string;
}

const legendItems: LegendItem[] = [
  { color: "#BA8376", key: "mountainForests", mapSrc: p1, bgSrc: pb1 },
  { color: "#C8585D", key: "redSea", mapSrc: p2, bgSrc: pb2 },
  { color: "#8B8295", key: "arabianGulf", mapSrc: p3, bgSrc: pb3 },
  { color: "#187584", key: "arabianSands", mapSrc: p4, bgSrc: pb4 },
  { color: "#864317", key: "westernCoastalPlain", mapSrc: p5, bgSrc: pb5 },
  { color: "#1F6747", key: "arabianGulfCoastalPlain", mapSrc: p6, bgSrc: pb6 },
  { color: "#A97D2B", key: "westernHighlands", mapSrc: p7, bgSrc: pb7 },
  { color: "#688D5E", key: "arabianPlateaus", mapSrc: p8, bgSrc: pb8 },
  { color: "#4F2D30", key: "northernRegionDesert", mapSrc: p9, bgSrc: pb9 },
];

// Type for eco points
interface EcoPoint {
  x: number;
  y: number;
  idx: number;
}

const ecoPoints: EcoPoint[] = [
  { x: 37, y: 63, idx: 0 },
  { x: 28, y: 60, idx: 1 },
  { x: 65, y: 29, idx: 2 },
  { x: 66, y: 60, idx: 3 },
  { x: 30, y: 52, idx: 4 },
  { x: 63, y: 40, idx: 5 },
  { x: 27, y: 40, idx: 6 },
  { x: 45, y: 45, idx: 7 },
  { x: 30, y: 17, idx: 8 },
];

/* 4. Helpers */

// TabButton Props
interface TabButtonProps {
  active: boolean;
  onClick: () => void;
  children: React.ReactNode;
}

const TabButton: React.FC<TabButtonProps> = ({ active, onClick, children }) => (
  <button className={`tab ${active ? "tab-active" : ""}`} onClick={onClick}>
    {children}
  </button>
);

// YearSelector Props
interface YearSelectorProps {
  years: string[];
  selected: string;
  onChange: (year: string) => void;
}

const YearSelector: React.FC<YearSelectorProps> = ({
  years,
  selected,
  onChange,
}) => (
  <div className="years">
    {years.map((y) => (
      <button
        key={y}
        className={`year-btn ${y === selected ? "year-btn-active" : ""}`}
        onClick={() => onChange(y)}
      >
        {y}
      </button>
    ))}
  </div>
);

// StaticColorBar Component
const StaticColorBar: React.FC = () => {
  const { t } = useTranslation();
  const [hovered, setHovered] = useState<number | null>(null);

  const legendItems = [
    { color: "#BA8376", key: "ncwProtected" },
    { color: "#8B8295", key: "alulaProtected" },
    { color: "#187584", key: "royalProtected" },
    { color: "#864317", key: "otherProtected" },
    { color: "#1F6747", key: "ncw2025" },
    { color: "#A97D2B", key: "other2025" },
    { color: "#688D5E", key: "ncw2030" },
  ];

  return (
    <div className="color-bar">
      {legendItems.map((item, i) => (
        <div
          key={item.key}
          className="color-item"
          style={{ backgroundColor: item.color }}
          onMouseEnter={() => setHovered(i)}
          onMouseLeave={() => setHovered(null)}
        >
          {hovered === i && (
            <div className="color-item-tooltip">
              {t(`map3.environmentalRegions.regions.${item.key}.title`)}
            </div>
          )}
        </div>
      ))}
    </div>
  );
};

/* 5. Main component */
const Map3: React.FC = () => {
  const { t } = useTranslation();
  const [tab, setTab] = useState<number>(1);
  const [year, setYear] = useState<string>("2017");
  const [activeRegion, setActiveRegion] = useState<number | null>(null);
  const [envBg, setEnvBg] = useState<string>(bg2);

  const backgroundImage = tab === 1 ? bg1 : envBg;

  const foregroundImage = useMemo(() => {
    if (tab === 1) {
      return year === "2030" ? img2030 : year === "2025" ? img2025 : img2017;
    }
    return activeRegion !== null ? legendItems[activeRegion].mapSrc : noBorder;
  }, [tab, year, activeRegion]);

  const handleMainTab = useCallback((t: number) => {
    setTab(t);
    setActiveRegion(null);
  }, []);

  const handleYear = useCallback((y: string) => setYear(y), []);

  const handlePointClick = useCallback((idx: number) => {
    setActiveRegion(idx);
    setEnvBg(legendItems[idx].bgSrc);
  }, []);

  useEffect(() => {
    const style = document.createElement("style");
    style.innerHTML = `
      @media(min-width:1024px) {
        .map3 .main {
          grid-template-columns: 1fr 340px;
          padding: 3rem 2rem 2rem;
        }
      }
    `;
    document.head.appendChild(style);
    return () => style.remove();
  }, []);

  const preloadSrcs: string[] = [
    bg1,
    bg2,
    img2017,
    img2025,
    img2030,
    noBorder,
    ...legendItems.flatMap((i) => [i.mapSrc, i.bgSrc]),
  ];

  return (
    <section
      className="map3 wrapper"
      style={{ backgroundImage: `url(${backgroundImage})` }}
    >
      <div className="preload">
        {preloadSrcs.map((s, i) => (
          <img key={i} src={s} alt="" style={{ display: "none" }} />
        ))}
      </div>

      <header className="header" />

      <main className="main">
        <div className="map-frame">
          <img className="map-img" src={foregroundImage} alt="map" />
          {tab === 1 && <StaticColorBar />}
          {tab === 2 && (
            <div className="eco-points-container">
              {ecoPoints.map((pt) => (
                <button
                  key={pt.idx}
                  className={`eco-point ${
                    activeRegion === pt.idx ? "eco-point-active" : ""
                  }`}
                  style={{
                    top: `${pt.y}%`,
                    left: `${pt.x}%`,
                  }}
                  onClick={() => handlePointClick(pt.idx)}
                />
              ))}
            </div>
          )}
        </div>

        <aside className="sidebar">
          <div className="tab-group">
            <TabButton active={tab === 1} onClick={() => handleMainTab(1)}>
              {t("map2.tabs.protectedAreas")}
            </TabButton>
            <TabButton active={tab === 2} onClick={() => handleMainTab(2)}>
              {t("map2.tabs.environmentalRegions")}
            </TabButton>
          </div>

          {tab === 1 && (
            <div className="protected-card">
              <div className="stats">
                <span>{t("map2.stats.seaArea")}</span>
                <strong className="stats-strong">{stats[year].sea}</strong>
              </div>
              <div className="stats">
                <span>{t("map2.stats.landArea")}</span>
                <strong className="stats-strong">{stats[year].land}</strong>
              </div>
              <YearSelector
                years={["2017", "2025", "2030"]}
                selected={year}
                onChange={handleYear}
              />
            </div>
          )}

          {activeRegion !== null && (
            <article className="region-card">
              <h3 className="region-card-h3">
                {t(
                  `map2.environmentalRegions.regions.${legendItems[activeRegion].key}.title`
                )}
              </h3>
              <p className="region-card-p">
                {t(
                  `map2.environmentalRegions.regions.${legendItems[activeRegion].key}.description`
                )}
              </p>
            </article>
          )}

          <Link to="/services/explorer" target="_blank" className="explorer">
            {t("interactiveMap.geoExplorer")}
            <svg
              width="20"
              height="20"
              fill="none"
              stroke="currentColor"
              strokeWidth={2}
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M18 13v6a2 2 0 01-2 2H6a2 2 0 01-2-2V8a2 2 0 012-2h6m5-3h3m0 0v6m0-6L10 14"
              />
            </svg>
          </Link>
        </aside>
      </main>
    </section>
  );
};

export default Map3;
