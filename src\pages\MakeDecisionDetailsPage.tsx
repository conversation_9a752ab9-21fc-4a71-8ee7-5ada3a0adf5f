import { useTranslation } from "react-i18next";
import ServiceDetailsLayout from "../components/layouts/ServiceDetailsLayout";
import SectionTitle from "../components/common/SectionTitle";
import ServiceTimeSection from "../components/services/ServiceTimeSection";
import { motion } from "framer-motion";
import { staggerContainer, cardVariant } from "../utils/animations";
// import logo from "../assets/images/e-services/explorer-logo.svg";

export default function MakeDecisionDetailsPage() {
  const { t } = useTranslation();

  // Custom beneficiaries list with grid layout
  const renderBeneficiariesList = () => {
    const beneficiaries = t("makeDecisionDetailsPage.beneficiaries.list", {
      returnObjects: true,
    }) as string[];

    return (
      <div>
        <SectionTitle
          title={t("makeDecisionDetailsPage.beneficiaries.title")}
        />

        <motion.div
          className="row g-4 explorer-list"
          initial="hidden"
          animate="visible"
          variants={staggerContainer}
        >
          {beneficiaries.map((ele, index) => (
            <motion.div
              className="col-12 col-md-6 col-lg-4"
              key={index}
              variants={cardVariant}
              transition={{ delay: index * 0.05 }}
            >
              <div className="explorer-card">
                {/* <img src={logo} alt="" /> */}
                <span>{ele}</span>
              </div>
            </motion.div>
          ))}
        </motion.div>
      </div>
    );
  };

  return (
    <ServiceDetailsLayout
      breadcrumbTitle={t("makeDecisionDetailsPage.breadcrumb")}
      downloadGuideText={t("makeDecisionDetailsPage.downloadGuide")}
      downloadCardContent={
        <a href="#" className="btn btn-outline-secondary">
          {t("makeDecisionDetailsPage.startService")}
        </a>
      }
    >
      <SectionTitle
        title={t("makeDecisionDetailsPage.title")}
        description={t("makeDecisionDetailsPage.description")}
      />

      <div>
        <SectionTitle title={t("makeDecisionDetailsPage.procedures.title")} />

        <ul className="section-list">
          {(
            t("makeDecisionDetailsPage.procedures.steps", {
              returnObjects: true,
            }) as string[]
          ).map((step, index) => (
            <li key={index} className="section-description">
              {step}
            </li>
          ))}
        </ul>
      </div>

      {renderBeneficiariesList()}

      {/* <div>
        <SectionTitle
          title={t("makeDecisionDetailsPage.terms.title")}
          description={t("makeDecisionDetailsPage.terms.description")}
        />
      </div> */}

      <ServiceTimeSection
        title={t("makeDecisionDetailsPage.serviceTime.title")}
        description={t("makeDecisionDetailsPage.serviceTime.description")}
      />
    </ServiceDetailsLayout>
  );
}
