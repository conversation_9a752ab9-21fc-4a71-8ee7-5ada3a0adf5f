import { ReactNode } from "react";
import { motion } from "framer-motion";
import { fadeInUp } from "../../utils/animations";

interface SectionTitleProps {
  title: string;
  description?: string | ReactNode;
  coloredPart?: string;
  className?: string;
  animationDelay?: number;
}

/**
 * A reusable section title component that supports colored text and animations
 */
const SectionTitle: React.FC<SectionTitleProps> = ({
  title,
  description,
  className = "",
  animationDelay = 0,
}) => {
  return (
    <div
      className={`section-header flex-column align-items-start ${className}`}
    >
      <motion.h5
        className="section-title"
        initial="hidden"
        animate="visible"
        variants={fadeInUp(animationDelay)}
      >
        {title}
      </motion.h5>

      {description && (
        <motion.div
          className="section-description"
          initial="hidden"
          animate="visible"
          variants={fadeInUp(animationDelay + 0.2)}
        >
          {description}
        </motion.div>
      )}
    </div>
  );
};

export default SectionTitle;
