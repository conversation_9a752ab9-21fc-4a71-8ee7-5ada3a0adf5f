import { useEffect, useRef } from "react";
import logo from "../../assets/images/logo.svg";
import logo2030 from "../../assets/images/2030-logo.svg";
import { Link, useLocation } from "react-router";
import { urls } from "../../utils/urls";
import { locale_atom } from "../../atoms/locale-atom";
import { useTranslation } from "react-i18next";
import logout_image from "../../assets/images/logout.svg";
import user_image from "../../assets/images/user.svg";

interface NavbarProps {
  onMenuToggle: () => void;
}

export default function Navbar({ onMenuToggle }: NavbarProps) {
  // Create a ref for the header element
  const headerRef = useRef(null);
  // Function to handle scroll events
  const scrollFunction = () => {
    // This function can be used for additional scroll-related functionality
    // Currently empty as per the requirements
  };

  useEffect(() => {
    // Get the header element using the ref
    const header = headerRef.current;

    // Function to handle scroll event
    const handleScroll = () => {
      // Check if the page has been scrolled more than 0 pixels
      if (window.scrollY > 0) {
        // Add the class to the header
        if (header) {
          (header as HTMLElement).classList.add("scrolled");
        }
      } else {
        // Remove the class if not scrolled
        if (header) {
          (header as HTMLElement).classList.remove("scrolled");
        }
      }
      scrollFunction();
    };

    // Add event listener when component mounts
    window.addEventListener("scroll", handleScroll);

    // Initial check to apply styles if page is already scrolled
    handleScroll();

    // Clean up event listener when component unmounts
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []); // Empty dependency array means this effect runs once on mount

  const { pathname } = useLocation();
  const { t } = useTranslation();
  const currentLocale = locale_atom.useValue();

  const toggleLanguage = () => {
    locale_atom.toggleLanguage();
    // Refresh the page after language change
    window.location.reload();
  };

  const handleLogout = () => {
    // Add your logout logic here
    console.log("Logging out...");
    // You can add your logout implementation here
    localStorage.removeItem("token");
    localStorage.removeItem("user");
  };

  const isLoggedIn = localStorage.getItem("user");

  return (
    <header ref={headerRef} className="main-header">
      <nav className="navbar navbar-expand-xl p-0">
        <div className="container-fluid">
          <div className="logo-container">
            <Link className="navbar-brand" to={"/"} title={t("navbar.home")}>
              <img src={logo} alt="" />
            </Link>
            <Link
              className="logo-2030"
              target="_blank"
              to={urls.vision_2030}
              title="KSA 2030"
            >
              <img src={logo2030} alt="" />
            </Link>
            <button
              className="btn-toggle d-inline-flex d-xl-none"
              type="button"
              onClick={onMenuToggle}
            >
              <svg width="78" height="52" viewBox="0 0 78 52" fill="none">
                <path
                  fill-rule="evenodd"
                  clip-rule="evenodd"
                  d="M2.71612 5.43223H38.931C39.6513 5.43223 40.3422 5.14607 40.8516 4.6367C41.3609 4.12733 41.6471 3.43647 41.6471 2.71612C41.6471 1.99576 41.3609 1.3049 40.8516 0.795533C40.3422 0.286163 39.6513 0 38.931 0H2.71612C1.99576 0 1.3049 0.286163 0.795531 0.795533C0.286161 1.3049 0 1.99576 0 2.71612C0 3.43647 0.286161 4.12733 0.795531 4.6367C1.3049 5.14607 1.99576 5.43223 2.71612 5.43223ZM77.862 25.6401C77.862 24.9197 77.5758 24.2289 77.0665 23.7195C76.5571 23.2101 75.8662 22.924 75.1459 22.924H2.71612C1.99576 22.924 1.3049 23.2101 0.795531 23.7195C0.286161 24.2289 0 24.9197 0 25.6401C0 26.3604 0.286161 27.0513 0.795531 27.5607C1.3049 28.07 1.99576 28.3562 2.71612 28.3562H75.1459C75.8662 28.3562 76.5571 28.07 77.0665 27.5607C77.5758 27.0513 77.862 26.3604 77.862 25.6401ZM38.931 45.848H75.1459C75.8662 45.848 76.5571 46.1342 77.0664 46.6436C77.5758 47.1529 77.862 47.8438 77.862 48.5642C77.862 49.2845 77.5758 49.9754 77.0664 50.4847C76.5571 50.9941 75.8662 51.2803 75.1459 51.2803H38.931C38.2106 51.2803 37.5198 50.9941 37.0104 50.4847C36.501 49.9754 36.2149 49.2845 36.2149 48.5642C36.2149 47.8438 36.501 47.1529 37.0104 46.6436C37.5198 46.1342 38.2106 45.848 38.931 45.848Z"
                  fill="#fff"
                />
              </svg>
            </button>
          </div>
          <div
            className="collapse navbar-collapse justify-content-center"
            id="navbarNavDropdown"
          >
            <ul className="navbar-nav">
              <li className="nav-item">
                <Link
                  className={`nav-link ${pathname === "/" && "active"}`}
                  aria-current="page"
                  to={"/"}
                >
                  {t("navbar.home")}
                </Link>
              </li>
              <li className="nav-item">
                <Link
                  className={`nav-link ${pathname === "/about" && "active"}`}
                  to="/about"
                >
                  {t("navbar.about")}
                </Link>
              </li>
              <li className="nav-item">
                <Link
                  className={`nav-link ${pathname === "/services" && "active"}`}
                  to="/services"
                >
                  {t("navbar.services")}
                </Link>
              </li>
              {/* <li className="nav-item">
                <Link className="nav-link" to="#">
                  التسجيل الوطني للمحميات{" "}
                </Link>
              </li> */}
              <li className="nav-item">
                <Link
                  className={`nav-link ${pathname === "/guides" && "active"}`}
                  to="/guides"
                >
                  {t("navbar.guides")}
                </Link>
              </li>
              {isLoggedIn && (
                <li className="nav-item">
                  <Link
                    className={`nav-link ${pathname === "/apps" && "active"}`}
                    to="/my-apps"
                  >
                    {t("apps.breadcrumb", "My Apps")}
                  </Link>
                </li>
              )}
            </ul>
          </div>
          <div className="header-actions">
            <Link to="#" onClick={toggleLanguage} className="language-toggle">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16.125"
                height="16.125"
                viewBox="0 0 16.125 16.125"
              >
                <path
                  id="Union_2"
                  data-name="Union 2"
                  d="M9.9,16.125a27.088,27.088,0,0,1-2.907-.089,3.582,3.582,0,0,1-1.941-.72,3.512,3.512,0,0,1-.494-.494,4.123,4.123,0,0,1-.768-2.488A4.136,4.136,0,0,1,1.3,11.567a3.662,3.662,0,0,1-.494-.494A3.581,3.581,0,0,1,.089,9.131,27.088,27.088,0,0,1,0,6.224V6.151A27.088,27.088,0,0,1,.089,3.244,3.581,3.581,0,0,1,.808,1.3,3.662,3.662,0,0,1,1.3.808,3.581,3.581,0,0,1,3.244.089,27.088,27.088,0,0,1,6.151,0h.074A27.088,27.088,0,0,1,9.131.089a3.581,3.581,0,0,1,1.941.719,3.662,3.662,0,0,1,.494.494,4.136,4.136,0,0,1,.768,2.489,4.123,4.123,0,0,1,2.488.768,3.512,3.512,0,0,1,.494.494,3.582,3.582,0,0,1,.72,1.941A27.088,27.088,0,0,1,16.125,9.9v.073a27.08,27.08,0,0,1-.089,2.907,3.579,3.579,0,0,1-.72,1.941,3.465,3.465,0,0,1-.494.494,3.579,3.579,0,0,1-1.941.72,27.08,27.08,0,0,1-2.907.089Zm-5-4.1a3.384,3.384,0,0,0,.527,2.088,2.454,2.454,0,0,0,.338.338,2.479,2.479,0,0,0,1.36.472A26.462,26.462,0,0,0,9.938,15a26.462,26.462,0,0,0,2.812-.082,2.475,2.475,0,0,0,1.359-.472,2.4,2.4,0,0,0,.338-.338,2.475,2.475,0,0,0,.472-1.359A26.462,26.462,0,0,0,15,9.938a26.462,26.462,0,0,0-.082-2.812,2.479,2.479,0,0,0-.472-1.36,2.454,2.454,0,0,0-.338-.338A3.366,3.366,0,0,0,12.036,4.9ZM3.376,1.206a2.485,2.485,0,0,0-1.36.473,2.392,2.392,0,0,0-.337.337,2.485,2.485,0,0,0-.473,1.36,26.758,26.758,0,0,0-.081,2.812A26.758,26.758,0,0,0,1.206,9a2.485,2.485,0,0,0,.473,1.36,2.392,2.392,0,0,0,.337.337,3.384,3.384,0,0,0,2.09.527l7.119-7.1A3.408,3.408,0,0,0,10.7,2.016a2.392,2.392,0,0,0-.337-.337A2.485,2.485,0,0,0,9,1.206a26.758,26.758,0,0,0-2.812-.081A26.758,26.758,0,0,0,3.376,1.206Zm8.667,12.323-.481-1.154H9.813l-.481,1.154A.563.563,0,1,1,8.293,13.1l1.875-4.5a.563.563,0,0,1,1.039,0l1.875,4.5a.563.563,0,1,1-1.039.433ZM10.282,11.25h.812l-.406-.975ZM3.129,8.443a.563.563,0,0,1,.033-.795A16.774,16.774,0,0,0,4.5,6.28c-.094-.121-.188-.246-.276-.368a5.107,5.107,0,0,1-.45-.715A.562.562,0,0,1,4.8,4.722a4.256,4.256,0,0,0,.343.532l.081.112A7.621,7.621,0,0,0,5.949,4.1H2.813a.563.563,0,1,1,0-1.125h1.8V2.813a.563.563,0,0,1,1.125,0v.162H8.062a.563.563,0,0,1,0,1.125h-.9A8.462,8.462,0,0,1,5.973,6.245l.676.7a.563.563,0,1,1-.811.78l-.58-.6A18.327,18.327,0,0,1,3.925,8.476a.563.563,0,0,1-.8-.033Z"
                  fill="#fff"
                />
              </svg>

              <span className="title">
                {currentLocale === "en" ? "AR" : "EN"}
              </span>
            </Link>

            {isLoggedIn ? (
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  gap: "16px",
                }}
              >
                <div className="user-info">
                  <img src={user_image} alt="user img" />
                  <span>
                    {JSON.parse(localStorage.getItem("user") || "{}").name}
                  </span>
                </div>

                <Link to="/" onClick={handleLogout}>
                  <img src={logout_image} alt="logout img" />
                </Link>
              </div>
            ) : (
              <Link to="/login" className="login-btn">
                <img src={user_image} alt="login img" />
                <span className="title">{t("navbar.login")}</span>
              </Link>
            )}
          </div>
        </div>
      </nav>
    </header>
  );
}
