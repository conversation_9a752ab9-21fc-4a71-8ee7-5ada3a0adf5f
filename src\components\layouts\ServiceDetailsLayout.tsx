import { ReactNode } from "react";
import { Link } from "react-router";
import { IoIosArrowBack } from "react-icons/io";
import { useTranslation } from "react-i18next";
import PageLayout from "./PageLayout";
import DownloadCard from "../services/DownloadCard";

interface ServiceDetailsLayoutProps {
  children: ReactNode;
  breadcrumbTitle: string;
  downloadGuideText: string;
  downloadCardContent?: ReactNode;
  className?: string;
  breadcrumbBgImage?: string;
}

/**
 * A specialized layout for service detail pages
 */
const ServiceDetailsLayout: React.FC<ServiceDetailsLayoutProps> = ({
  children,
  breadcrumbTitle,
  downloadGuideText,
  downloadCardContent,
  className = "",
  breadcrumbBgImage,
}) => {
  const { t } = useTranslation();

  const breadcrumbContent = (
    <>
      <Link to="/services">{t("services.breadcrumb")}</Link>
      <IoIosArrowBack />
      <h2>{breadcrumbTitle}</h2>
    </>
  );

  return (
    <PageLayout
      breadcrumbContent={breadcrumbContent}
      backgroundAlt="Map Vector"
      className={className}
      breadcrumbBgImage={breadcrumbBgImage}
    >
      <div className="container-fluid">
        <div className="row g-3 align-items-start my-5">
          <div className="col">{children}</div>

          <div className="col-lg-3 g-0 d-flex justify-content-lg-end">
            <DownloadCard title={downloadGuideText}>
              {downloadCardContent}
            </DownloadCard>
          </div>
        </div>
      </div>
    </PageLayout>
  );
};

export default ServiceDetailsLayout;
