import { useTranslation } from "react-i18next";
import ServiceDetailsLayout from "../components/layouts/ServiceDetailsLayout";
import SectionTitle from "../components/common/SectionTitle";
import BeneficiariesList from "../components/services/BeneficiariesList";
import ServiceTimeSection from "../components/services/ServiceTimeSection";
import { urls } from "../utils/urls";

export default function RequestDataDetailsPage() {
  const { t } = useTranslation();

  const descriptionWithServices = (
    <>
      {t("requestDataDetailsPage.description")}{" "}
      <ul className="section-list">
        {(
          t("requestDataDetailsPage.services", {
            returnObjects: true,
          }) as string[]
        ).map((service: string, index: number) => (
          <li key={index} className="section-description">
            {service}
          </li>
        ))}
      </ul>
    </>
  );

  return (
    <ServiceDetailsLayout
      breadcrumbTitle={t("requestDataDetailsPage.breadcrumb")}
      downloadGuideText={t("requestDataDetailsPage.downloadGuide")}
      downloadCardContent={
        <a
          href={
            localStorage.getItem("token")
              ? urls.requestData + localStorage.getItem("token")
              : "/wildlife-portal/login"
          }
          className="btn btn-outline-secondary"
        >
          {t("requestDataDetailsPage.startService")}
        </a>
      }
    >
      <SectionTitle
        title={t("requestDataDetailsPage.title")}
        description={descriptionWithServices}
      />

      <div>
        <SectionTitle title={t("requestDataDetailsPage.procedures.title")} />

        <ul
          className="section-list"
          style={{
            display: "grid",
            gridTemplateColumns: "repeat(auto-fit, minmax(400px, 1fr))",
            width: "100%",
          }}
        >
          {(
            t("requestDataDetailsPage.procedures.steps", {
              returnObjects: true,
            }) as string[]
          ).map((step: string, index: number) => (
            <li key={index} className="section-description">
              {step}
            </li>
          ))}
        </ul>
      </div>

      <BeneficiariesList
        title={t("requestDataDetailsPage.beneficiaries.title")}
        beneficiaries={
          t("requestDataDetailsPage.beneficiaries.list", {
            returnObjects: true,
          }) as string[]
        }
      />

      {/* <div>
        <SectionTitle
          title={t("requestDataDetailsPage.terms.title")}
          description={t("requestDataDetailsPage.terms.description")}
        />
      </div> */}

      <ServiceTimeSection
        title={t("requestDataDetailsPage.serviceTime.title")}
        description={t("requestDataDetailsPage.serviceTime.description")}
      />
    </ServiceDetailsLayout>
  );
}
