import Services from "../components/home/<USER>";
import PortalStatistics from "../components/home/<USER>";
import { useEffect, useRef, useState } from "react";
import { scrollToTop } from "../utils/helpers";

import ContactUsSection from "../components/layouts/ContactUsSection.tsx";
import FooterLinksSection from "../components/layouts/FooterLinksSection.tsx";
import MobileMenu from "../components/home/<USER>";
import { Swiper, SwiperSlide } from "swiper/react";
import { EffectFade, Keyboard, Mousewheel } from "swiper/modules";
import Navbar from "../components/layouts/Navbar.js";
import Hero2 from "../components/home/<USER>";
import Map3 from "../components/home/<USER>/map3.tsx";
import Map2 from "../components/home/<USER>/Map2";
import useSmallScreen from "../hooks/use-small-screen.ts";
import HomeFooter from "../components/home/<USER>";

/**
 * Custom Pagination Component for Home Vertical Swiper
 */
const CustomPagination = ({
  activeIndex,
  totalSlides,
  onBulletClick,
}: {
  activeIndex: number;
  totalSlides: number;
  onBulletClick: (index: number) => void;
}) => {
  return (
    <div className="custom-pagination">
      {Array.from({ length: totalSlides }, (_, index) => (
        <span
          key={index}
          className={`swiper-pagination-bullet ${
            index === activeIndex ? "swiper-pagination-bullet-active" : ""
          }`}
          onClick={() => onBulletClick(index)}
          data-slide={index + 1}
        />
      ))}
    </div>
  );
};

/**
 * HomePage component with vertical carousel
 * This page includes the Hero, Map, Services, Statistics, Contact Us, and Footer Links sections
 */
export default function HomePage() {
  const swiperRef = useRef<any>(null);
  const [activeSlide, setActiveSlide] = useState(0);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  // Scroll to top when the component mounts
  useEffect(() => {
    scrollToTop();

    // Add carousel-active class to body
    document.body.classList.add("carousel-active");

    return () => {
      // Remove carousel-active class when component unmounts
      document.body.classList.remove("carousel-active");

      // Reset navbar to default state when leaving home page
      const header = document.querySelector(".main-header");
      if (header) {
        header.classList.remove("scrolled");
      }
    };
  }, []);

  // Handle slide change
  const handleSlideChange = (swiper: any) => {
    const newActiveSlide = swiper.activeIndex;
    setActiveSlide(newActiveSlide);

    // Handle navbar styling based on active slide
    const header = document.querySelector(".main-header");
    if (header) {
      // Add scrolled class for sections with light backgrounds
      // Services section (index 2) has white background
      // Statistics section (index 3) has light beige background
      if (newActiveSlide === 2 || newActiveSlide === 3) {
        header.classList.add("scrolled");
      } else {
        header.classList.remove("scrolled");
      }
    }
  };

  // Handle bullet click
  const handleBulletClick = (index: number) => {
    if (swiperRef.current && swiperRef.current.swiper) {
      swiperRef.current.swiper.slideTo(index);
    }
  };

  const { isSmallScreen } = useSmallScreen(991);

  return (
    <>
      <Navbar onMenuToggle={() => setIsMobileMenuOpen(!isMobileMenuOpen)} />

      {/* Custom Pagination */}
      <CustomPagination
        activeIndex={activeSlide}
        totalSlides={6}
        onBulletClick={handleBulletClick}
      />

      {/* Vertical Carousel */}
      <Swiper
        ref={swiperRef}
        direction="vertical"
        slidesPerView={1}
        spaceBetween={0}
        mousewheel={{
          thresholdDelta: 50,
          sensitivity: 1,
        }}
        keyboard={{
          enabled: true,
          onlyInViewport: true,
        }}
        speed={1000}
        modules={[Mousewheel, Keyboard, EffectFade]}
        className="home-vertical-swiper"
        onSlideChange={handleSlideChange}
        style={{ height: "100vh" }}
      >
        <SwiperSlide id="hero-section">
          <Hero2 />
        </SwiperSlide>

        <SwiperSlide id="map-section">
          {isSmallScreen ? <Map3 /> : <Map2 />}
        </SwiperSlide>

        <SwiperSlide id="services-section">
          <Services />
        </SwiperSlide>

        <SwiperSlide id="statistics-section">
          <PortalStatistics />
        </SwiperSlide>

        {/* <SwiperSlide id="contact-us-section">
          <ContactUsSection />
        </SwiperSlide>

        <SwiperSlide id="footer-links-section">
          <FooterLinksSection />
        </SwiperSlide> */}

        <SwiperSlide id="home-footer">
          <HomeFooter />
        </SwiperSlide>
      </Swiper>

      <MobileMenu
        isOpen={isMobileMenuOpen}
        onClose={() => setIsMobileMenuOpen(false)}
      />
    </>
  );
}
