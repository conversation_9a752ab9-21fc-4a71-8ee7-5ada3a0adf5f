import { useTranslation } from "react-i18next";
import { FaPaw, FaArrowRight } from "react-icons/fa6";
import { useNavigate } from "react-router";
import { useEffect, useState } from "react";

const NotFoundPage = () => {
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const [isRTL, setIsRTL] = useState(false);

  useEffect(() => {
    setIsRTL(document.documentElement.dir === "rtl");
  }, [i18n.language]);

  // Color palette from Map3 component
  const colors = {
    primary: "#b45434",
    secondary: "#8B8295",
    accent: "#187584",
    dark: "#1A1A1A",
    light: "#F5F5F5",
  };

  return (
    <div
      style={{
        minHeight: "100vh",
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
        background: `linear-gradient(135deg, ${colors.dark} 0%, #2D2D2D 50%, ${colors.dark} 100%)`,
        color: colors.light,
        position: "relative",
        overflow: "hidden",
        padding: "2rem",
        textAlign: "center",
      }}
    >
      {/* Decorative elements */}
      <div
        style={{
          position: "absolute",
          top: "-50%",
          left: "-50%",
          width: "100%",
          height: "200%",
          background: `radial-gradient(circle at 30% 30%, ${colors.accent}20 0%, transparent 50%)`,
          opacity: 0.6,
          transform: "rotate(45deg)",
          zIndex: 0,
        }}
      />

      <div
        style={{
          position: "relative",
          zIndex: 1,
          maxWidth: "800px",
          padding: "3rem 2rem",
          borderRadius: "24px",
          backgroundColor: "rgba(26, 26, 26, 0.7)",
          backdropFilter: "blur(10px)",
          border: "1px solid rgba(255, 255, 255, 0.1)",
          boxShadow: "0 8px 32px rgba(0, 0, 0, 0.3)",
        }}
      >
        <div
          style={{
            position: "relative",
            marginBottom: "2.5rem",
            display: "inline-flex",
            flexDirection: "column",
            alignItems: "center",
          }}
        >
          <div
            style={{
              position: "absolute",
              width: "200px",
              height: "200px",
              borderRadius: "50%",
              background: `radial-gradient(circle, ${colors.primary}40 0%, transparent 70%)`,
              filter: "blur(20px)",
              top: "50%",
              left: "50%",
              transform: "translate(-50%, -50%)",
              zIndex: -1,
            }}
          />
          <FaPaw
            size={80}
            style={{
              color: colors.primary,
              marginBottom: "1.5rem",
              filter: "drop-shadow(0 0 10px rgba(186, 131, 118, 0.5))",
            }}
          />
        </div>

        <h1
          style={{
            fontSize: "clamp(3.5rem, 12vw, 6rem)",
            fontWeight: 900,
            margin: "0 0 1rem",
            background: `linear-gradient(135deg, ${colors.primary}, ${colors.accent})`,
            WebkitBackgroundClip: "text",
            WebkitTextFillColor: "transparent",
            lineHeight: 1.1,
            letterSpacing: "-0.03em",
          }}
        >
          404
        </h1>

        <h2
          style={{
            fontSize: "clamp(1.5rem, 5vw, 2.25rem)",
            fontWeight: 700,
            margin: "0 0 1.5rem",
            color: colors.light,
            lineHeight: 1.2,
          }}
        >
          {t("notFound.title")}
        </h2>

        <p
          style={{
            fontSize: "clamp(1rem, 3vw, 1.125rem)",
            lineHeight: 1.7,
            maxWidth: "600px",
            margin: "0 auto 2.5rem",
            color: "rgba(255, 255, 255, 0.8)",
          }}
        >
          {t("notFound.description")}
        </p>

        <button
          onClick={() => navigate("/")}
          style={{
            display: "inline-flex",
            alignItems: "center",
            gap: "0.75rem",
            padding: "0.875rem 2rem",
            fontSize: "1.1rem",
            fontWeight: 600,
            color: "white",
            backgroundColor: colors.primary,
            border: `2px solid ${colors.primary}`,
            borderRadius: "50px",
            cursor: "pointer",
            transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
            boxShadow: `0 4px 15px ${colors.primary}40`,
            transform: "translateY(0)",
          }}
          onMouseOver={(e) => {
            e.currentTarget.style.backgroundColor = "transparent";
            e.currentTarget.style.color = colors.primary;
            e.currentTarget.style.transform = "translateY(-3px)";
            e.currentTarget.style.boxShadow = `0 8px 20px ${colors.primary}60`;
          }}
          onMouseOut={(e) => {
            e.currentTarget.style.backgroundColor = colors.primary;
            e.currentTarget.style.color = "white";
            e.currentTarget.style.transform = "translateY(0)";
            e.currentTarget.style.boxShadow = `0 4px 15px ${colors.primary}40`;
          }}
        >
          {t("notFound.button")}
          <FaArrowRight
            style={{
              transform: isRTL ? "rotate(180deg)" : "none",
              transition: "transform 0.3s ease",
            }}
          />
        </button>
      </div>
    </div>
  );
};

export default NotFoundPage;
